import styled from 'styled-components';

export const PageMain = styled.main`
  padding: ${({ theme }) => theme.spacings.xxs};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xs};

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    padding: ${({ theme }) => theme.spacings.xs};
  }
`;

export const EmptySubmenu = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.3rem;
  height: 100%;
  width: 100%;
  padding: 1rem;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.gray_40};
  text-align: center;
`;
