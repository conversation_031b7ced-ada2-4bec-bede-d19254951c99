import styled from 'styled-components';

export const Container = styled.footer`
  & > p {
    text-align: center;
    padding: 1rem;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    & > p {
      text-align: start;
    }
  }
`;

export const Content = styled.section`
  border-top: 1px solid ${({ theme }) => theme.colors.gray_90};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray_90};
  padding: 1rem 0 2.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    padding: 3rem 0;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
`;

export const Column = styled.div`
  & > h4 {
    text-align: center;
    white-space: nowrap;
    margin-bottom: 1rem;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    & > h4 {
      text-align: left;
      white-space: nowrap;
      margin-bottom: 1.75rem;
    }
  }
`;

export const Links = styled.div`
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    align-items: flex-start;
  }

  & > a {
    font-size: 1rem;
    text-decoration: none;
    transition: color 0.5s;

    &:hover {
      color: ${({ theme }) => theme.colors.warning_dark};
      filter: brightness(1.125);
    }
  }
`;
