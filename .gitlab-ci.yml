stages:
  - build
  - test
  - deploy

include:
  - template: Jobs/SAST.gitlab-ci.yml

build-staging:
  environment:
    name: staging
  image: node:20
  stage: build
  before_script:
    - cp ${ENV} .env
  script:
    - yarn install
    - yarn build:prod
  artifacts:
    paths:
      - dist/

build:
  only:
    - main
  environment:
    name: production
  image: node:20
  stage: build
  before_script:
    - echo "ENV is $ENV"
    - ls -la
    - cp ${ENV} .env
  script:
    - yarn install
    - yarn build:prod
  artifacts:
    paths:
      - dist/

deploy-staging:
  image: ubuntu:latest
  stage: deploy
  only:
    - dev
  environment:
    name: staging
  before_script:
    - apt-get -yq update
    - apt-get -yqq install ssh
    - eval `ssh-agent`
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - install -m 600 -D /dev/null ~/.ssh/id_rsa
    - echo "$APP_SERVICE_KEY" | base64 -d > ~/.ssh/id_rsa
    - touch ~/.ssh/known_hosts
    - chmod 600 ~/.ssh/known_hosts
  script:
    - echo "Deploying to Cloud App Service..."
    - ssh-keyscan -p 8722 -t rsa -H $APP_SERVICE_HOST > ~/.ssh/known_hosts
    - scp -v -P 8722 -r dist/* $APP_SERVICE_USER@$APP_SERVICE_HOST:/ || true
    - echo "Finished deploy to Cloud App Service!"

deploy:
  image: ubuntu:latest
  stage: deploy
  only:
    - main
  environment:
    name: production
  before_script:
    - apt-get -yq update
    - apt-get -yqq install ssh
    - eval `ssh-agent`
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - install -m 600 -D /dev/null ~/.ssh/id_rsa
    - echo "$APP_SERVICE_KEY" | base64 -d > ~/.ssh/id_rsa
    - touch ~/.ssh/known_hosts
    - chmod 600 ~/.ssh/known_hosts
  script:
    - echo "Deploying to Cloud App Service..."
    - ssh-keyscan -p 8722 -t rsa -H $APP_SERVICE_HOST > ~/.ssh/known_hosts
    - scp -v -P 8722 -r dist/* $APP_SERVICE_USER@$APP_SERVICE_HOST:/ || true
    - echo "Finished deploy to Cloud App Service!"
