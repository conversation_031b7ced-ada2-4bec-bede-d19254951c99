/* eslint-disable @typescript-eslint/no-explicit-any */
import { formatters } from '@/utils/formatters';
import { RemoteLoadAvailableDates } from './types';
import { httpClient } from '@/services/httpClient';

export const remoteLoadAvailableDates: RemoteLoadAvailableDates = async (
  params,
) => {
  const today = new Date();
  const startDate = params.dataInicio || today;
  const endDate =
    params.dataFim ||
    new Date(today.getFullYear(), today.getMonth(), today.getDate() + 30);

  const parametro = {
    empresa: '257308',
    codigo: '159111',
    chave: '3f1525a4308ad1308944',
    tipoSaida: params.tipoSaida || 'json',
    empresaTrabalho: params.codigoSoc,
    dataInicio: startDate.toLocaleDateString('pt-BR'),
    dataFim: endDate.toLocaleDateString('pt-BR'),
    codigoAgenda: params.codigoAgenda,
    statusAgendaFiltro: '1',
  };

  const urlSearchParams = new URLSearchParams({
    parametro: JSON.stringify(parametro),
  });

  const response = await httpClient.get('/soc/exportardados', {
    params: {
      parametros: `${urlSearchParams.toString()}`,
    },
  });
  return {
    statusCode: 200,
    title: 'Pesquisa concluída',
    message: 'Pesquisa concluída com sucesso',
    errorType: null,
    hasError: false,
    result: response.data.map((item: any) => ({
      ...item,
      dateTyped: formatters.datePTBRToType(item.data),
    })),
  };
};
