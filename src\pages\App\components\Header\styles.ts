import styled, { css } from 'styled-components';

export const Container = styled.header`
  ${({ theme }) => css`
    color: ${theme.colors.gray_40};
    background-color: ${theme.colors.white};
    border-bottom: 1px solid ${theme.colors.gray_94};
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;
  `}
`;

export const Box = styled.div`
  width: 100%;
  display: flex;
  gap: 1rem;
  justify-content: space-between;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    justify-content: flex-end;
  }
`;

export const UserBox = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.25rem;
`;
