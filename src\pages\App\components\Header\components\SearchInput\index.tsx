import { useEffect, useState } from 'react';
import { InputText } from '@onyma-ds/react';
import { Icons } from '@/components';
import { useWindowWidth } from '@/hooks';
import * as SC from './styles';

export default function SearchInput() {
  const { isMobile } = useWindowWidth();

  const [showInput, setShowInput] = useState(false);

  useEffect(() => {
    setShowInput(!isMobile);
  }, [isMobile]);

  return (
    <SC.Container>
      {isMobile && (
        <SC.ControlInputView
          type="button"
          onClick={() => setShowInput((prev) => !prev)}
        >
          <Icons.Symbol
            size={18}
            name={showInput ? 'close' : 'search'}
          />
        </SC.ControlInputView>
      )}
      {showInput && (
        <SC.InputBox data-platform={isMobile ? 'mobile' : 'desktop'}>
          <InputText placeholder="Pesquisar..." />
          <SC.IconBox
            type="button"
            title="Pesquisar"
          >
            <Icons.Symbol
              name="search"
              size={24}
            />
          </SC.IconBox>
        </SC.InputBox>
      )}
    </SC.Container>
  );
}
