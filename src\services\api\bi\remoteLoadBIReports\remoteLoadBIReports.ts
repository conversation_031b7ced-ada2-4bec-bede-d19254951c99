/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadBIReports } from './types';

export const remoteLoadBIReports: RemoteLoadBIReports = async () => {
  const response = await httpClient.get('/bi/fetchall');
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: response.data.result.map((report: any) => ({
      id: report.id,
      name: report.nome,
      description: report.descricao,
      powerBiId: report.powerBiId,
      biPage: report.biPagina.map((item: any) => ({
        id: item.id,
        biId: item.biId,
        name: item.nome,
        description: item.descricao,
        powerBiName: item.powerBiNome,
      })),
    })),
  };
};
