import { Dialog } from '@onyma-ds/react';

import { useApi } from '@/contexts/api';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { ClientCompaniesContextProvider } from '../../../ClientCompanies/contexts/clientCompanies';
import { EditMenuForm } from './Form';
import * as SC from './styles';
import { EditMenuProps } from './types';

export default function EditPageModal({ children, menuId }: EditMenuProps) {
  const [isOpen, setIsOpen] = useState(false);

  const {
    menus: { loadMenu },
  } = useApi();

  const { data: currentMenu } = useQuery({
    queryKey: ['menu', menuId],
    queryFn: () => loadMenu({ id: menuId }),
  });

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={() => setIsOpen((state) => !state)}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay>
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">Editar menu</Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <ClientCompaniesContextProvider>
                <EditMenuForm
                  onClose={() => setIsOpen((state) => !state)}
                  defaultValues={currentMenu?.result}
                />
              </ClientCompaniesContextProvider>
            </Dialog.Body>
          </SC.Content>
        </Dialog.Overlay>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
