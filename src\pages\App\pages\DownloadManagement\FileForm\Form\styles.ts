import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

export const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const WrapperButtons = styled.div`
  width: 100%;

  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  button {
    flex: 50%;
  }
`;

export const FileUploadWrapper = styled.div`
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0.4rem 0.5rem;
  background-color: #f9f9f9;

  &:focus-within {
    border-color: #007bff;
  }
`;

export const TextInput = styled.input`
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 0.875rem;
  color: #aaa;

  &::placeholder {
    color: #ccc;
  }
`;

export const StyledLabel = styled.label`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  gap: 0.5rem;

  span {
    font-size: 0.875rem;
  }

  height: 2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #003f7f;
  }

  & svg {
    font-size: 20px;
  }
`;

export const StyledInput = styled.input`
  display: none;
`;
