import { useEffect, useRef, useState } from 'react';
import * as SC from './styles';

import { useAuth } from '@/contexts/auth';
import { BiChevronDown } from 'react-icons/bi';
import { FaUser } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

export default function UserDropdown() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpen((prev) => !prev);

  const handleSignOut = () => {
    logout();
  };

  // Detecta cliques fora do dropdown e fecha o menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup: Remove o event listener quando o componente for desmontado
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <SC.DropdownContainer ref={dropdownRef}>
      <div
        className="avatar"
        onClick={toggleDropdown}
      >
        <SC.AvatarContainer>
          <FaUser />
        </SC.AvatarContainer>
        <BiChevronDown />
      </div>
      {isOpen && (
        <SC.DropdownMenu>
          <SC.EmailText>{user.email}</SC.EmailText>
          <hr />
          <span onClick={() => navigate('/app/perfil')}>Perfil</span>
          <SC.SignOutButton
            title="Sair"
            type="button"
            onClick={handleSignOut}
          >
            <span>Sair</span>
          </SC.SignOutButton>
        </SC.DropdownMenu>
      )}
    </SC.DropdownContainer>
  );
}
