import * as z from 'zod';

const requiredMessage = 'Esse campo é obrigatório';

export const uploadFileSchema = z.object({
  file: z
    .instanceof(File, { message: requiredMessage })
    .refine((file) => !!file, { message: 'O arquivo é obrigatório.' })
    .refine(
      (file) =>
        [
          'text/csv', // MIME type mais comum para CSV
          'application/csv',
        ].includes(file.type),
      {
        message: 'O arquivo deve ser PDF, CSV ou Excel (XLS/XLSX).', // Mensagem atualizada
      },
    ),
  formId: z.object(
    {
      label: z.string(),
      value: z.string(),
    },
    { message: requiredMessage },
  ),
});

export type UploadFileSchemaType = z.infer<typeof uploadFileSchema>;
