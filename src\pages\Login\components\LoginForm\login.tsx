import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth';
import { purify } from '@/utils/purify';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@onyma-ds/react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';

import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { loginFormSchema, LoginFormValues } from './validation';
import { ChevronRight } from 'lucide-react';

export default function LoginFormPage() {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const { login } = useAuth();
  const {
    control,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
    handleSubmit,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      username: '',
      password: '',
      keepSession: false,
    },
  });

  const onSubmit: SubmitHandler<LoginFormValues> = async (data) => {
    try {
      clearErrors('root');
      const loginResult = await login({
        username: data.username,
        password: data.password,
        keepSession: data.keepSession,
      });
      addToast({
        type: 'success',
        title: loginResult.title,
        description: loginResult.message,
        timeout: 5000,
      });
      if (loginResult.result.isFirstLogin) {
        navigate('/primeiro-acesso', { replace: true });
        return;
      }

      navigate('/app', { replace: true });
    } catch (error) {
      const message = error.response?.data?.message;
      setError('root', { message });
      addToast({
        type: 'error',
        title: 'Erro ao entrar',
        description: 'Erro ao tentar fazer login. Tente novamente mais tarde',
        timeout: 5000,
      });
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-12 w-[360px]"
    >
      <h1 className="text-center text-2xl font-semibold">
        Acessar minha conta
      </h1>

      <div className="flex flex-col gap-8">
        <Controller
          control={control}
          name="username"
          render={({
            field: { ref, name, value, disabled, onBlur, onChange },
            fieldState: { error },
          }) => (
            <Input
              label="Login"
              placeholder="Informe o seu login"
              errorMessage={error?.message}
              ref={ref}
              name={name}
              value={value}
              disabled={disabled}
              onBlur={onBlur}
              onChange={({ target: { value } }) =>
                onChange(purify.sanitize(value))
              }
            />
          )}
        />
        <Controller
          control={control}
          name="password"
          render={({
            field: { ref, name, value, disabled, onBlur, onChange },
            fieldState: { error },
          }) => (
            <Input
              label="Senha"
              type="password"
              id="password"
              placeholder="Informe a sua senha"
              ref={ref}
              name={name}
              value={value}
              disabled={disabled}
              onBlur={onBlur}
              errorMessage={error?.message}
              onChange={({ target: { value } }) =>
                onChange(purify.sanitize(value))
              }
            />
          )}
        />
        <Controller
          control={control}
          name="keepSession"
          render={({ field: { name, value, disabled, onBlur, onChange } }) => (
            <div className="flex items-center gap-2">
              <Checkbox
                id="keepSession"
                name={name}
                checked={value}
                disabled={disabled}
                onBlur={onBlur}
                onChange={onChange}
                onCheckedChange={onChange}
              />
              <Label
                htmlFor="keepSession"
                className="cursor-pointer font-normal"
              >
                Lembre de mim
              </Label>
            </div>
          )}
        />
      </div>
      <div className="flex flex-col gap-4">
        <Button
          type="submit"
          disabled={!!errors.username || !!errors.password || isSubmitting}
          isLoading={isSubmitting}
        >
          Entrar
        </Button>
        <Link
          className="text-center w-full"
          to="/esqueci-minha-senha"
        >
          <Button
            className="text-primary hover:text-primary/95 w-full"
            variant="ghost"
            type="button"
            endIcon={<ChevronRight />}
          >
            Esqueci minha senha
          </Button>
        </Link>
        <div className="text-center text-sm text-gray-700">
          ou{' '}
          <Link
            to="/solicitar-acesso"
            className="hover:underline"
          >
            Solicitar acesso
          </Link>
        </div>
      </div>
    </form>
  );
}
