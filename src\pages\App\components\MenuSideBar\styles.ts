import styled from 'styled-components';

const minWidthOpen = '250px';
const timeAnimation = '0.3s';

export const Container = styled.aside`
  height: 100%;
  max-height: 100vh;
  min-width: ${minWidthOpen};
  background-color: ${({ theme }) => theme.colors.white};
  font-size: 0.875rem;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 8px 15px;
  border-left: 1px solid ${({ theme }) => theme.colors.gray_94};
  overflow-x: hidden;
  transform: translateX(-100%);
  transition: transform ${timeAnimation};
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  background-image: url('/imgs/ben.png');
  background-position: 0;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.5);
  }

  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;

  &[data-state='open'] {
    transform: translate(0);
    z-index: 2;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    position: relative;
    transform: translate(0);
  }
`;

export const ButtonCloseMenu = styled.button`
  all: unset;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  cursor: pointer;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    display: none;
  }
`;

export const Overlay = styled.div`
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
`;
