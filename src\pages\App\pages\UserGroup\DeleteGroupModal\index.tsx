import { Dialog } from '@onyma-ds/react';

import DeleteUserLayout from './Form';
import * as SC from './styles';
import { DeleteGroupModalProps } from './types';
import DeleteUserErrorMessage from './ErrorMessage';
import { useState } from 'react';

export default function DeleteUserGroupModal({
  isOpen,
  setModalState,
  groupId,
}: DeleteGroupModalProps) {
  const [hasError, setHasError] = useState(false);

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">
              Excluir grupo de usuários
            </Dialog.Title>
          </Dialog.Header>
          <Dialog.Body>
            {hasError ? (
              <DeleteUserErrorMessage onClose={setModalState} />
            ) : (
              <DeleteUserLayout
                setHasError={setHasError}
                onClose={setModalState}
                groupId={groupId}
              />
            )}
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
