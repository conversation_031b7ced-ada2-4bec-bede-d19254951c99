import { Dialog } from '@onyma-ds/react';

import * as SC from './styles';
import { NewGroupModalProps } from './types';
import { NewAgendaForm } from './Forms';

export default function NewAgendaModal({
  isOpen,
  setModalState,
}: NewGroupModalProps) {
  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">Nova agenda</Dialog.Title>
            <Dialog.Subtitle placeholder="">
              Preencha os dados abaixo
            </Dialog.Subtitle>
          </Dialog.Header>
          <Dialog.Body>
            <NewAgendaForm onClose={setModalState} />
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
