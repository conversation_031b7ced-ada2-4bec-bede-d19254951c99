import { ApiResult } from '@/@types/ApiResult';

export type User = {
  codigoSoc: string | null;
  companies: {
    id: string;
    nome: string;
  }[];
  email: string;
  fullName: string;
  id: string;
  isActive: boolean;
  lastRole: string;
  matriculaSoc: string | null;
  roles: {
    roleId: string;
    roleName: string;
  }[];
};

type Params = {
  id: string;
};

type Result = ApiResult<User>;

export type RemoteLoadUser = (params: Params) => Promise<Result>;
