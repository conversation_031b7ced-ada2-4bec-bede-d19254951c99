import { httpClient } from '@/services/httpClient';
import { RemoteChangePassword } from './types';

export const remoteChangePassword: RemoteChangePassword = async (params) => {
  const response = await httpClient.post('/user/changepassword', {
    UserUuid: params.userId,
    CurrentPassword: params.currentPassword,
    Password: params.password,
    PasswordRepeat: params.passwordRepeat,
    CheckCurrent: params.checkCurrent,
  });
  return response.data;
};
