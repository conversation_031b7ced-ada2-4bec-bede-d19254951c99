import { InputTextBox, Switch, TextareaBox } from '@onyma-ds/react';
import * as SC from './styles';

export default function UserProfileForm() {
  return (
    <SC.Container>
      <InputTextBox
        label="Nome*"
        placeholder="Digite o nome do médico cliente"
      />
      <TextareaBox
        label="Descrição"
        placeholder="Digite a descrição do médico cliente"
      />
      <SC.Fieldset>
        <InputTextBox
          label="Página inicial*"
          placeholder="Informe a página inicial do perfil"
        />
        <InputTextBox
          label="Ícone*"
          placeholder="Informe a página inicial do perfil"
        />
      </SC.Fieldset>
      <SC.Fieldset>
        <SC.SwitchContainer>
          <Switch />
          <Switch.Label>Requer empresa?</Switch.Label>
        </SC.SwitchContainer>
      </SC.Fieldset>
    </SC.Container>
  );
}
