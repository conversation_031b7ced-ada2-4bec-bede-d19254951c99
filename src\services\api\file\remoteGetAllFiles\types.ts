import { ApiResult } from '@/@types/ApiResult';

export type IFile = {
  id: string;
  nome: string;
  dataInicio: string;
  dataFim: string;
  dataCadastro: string;
  url: string;
  empresa: {
    id: string;
    nome: string;
  };
  tipo: {
    id: string;
    nome: string;
  };
  perfis: {
    id: string;
    nome: string;
  }[];
};

type Params = {
  id?: string;
  idEmpresaCliente?: string;
  idPerfil?: string;
  nome?: string;
  dataInicio?: string;
  dataFim?: string;
};

export type remoteFetchAllFile = (
  params: Params,
) => Promise<ApiResult<IFile[]>>;
