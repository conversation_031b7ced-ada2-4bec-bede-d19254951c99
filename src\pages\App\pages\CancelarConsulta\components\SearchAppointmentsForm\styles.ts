import styled, { DefaultTheme, css } from 'styled-components';

export const SearchAppointmentsForm = styled.form`
  max-width: 720px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xs};
`;

export const Grid = styled.div<{
  $columns?: number;
  $rows?: number;
  $autoFlow?: 'column' | 'row';
  $gap?: keyof DefaultTheme['spacings'];
}>`
  display: grid;
  gap: ${({ theme, $gap = 'xs' }) => theme.spacings[$gap]};
  ${({ $columns }) =>
    $columns &&
    css`
      grid-template-columns: repeat(${$columns}, 1fr);
    `}
  ${({ $rows }) =>
    $rows &&
    css`
      grid-template-rows: repeat(${$rows}, 1fr);
    `}
  ${({ $autoFlow }) =>
    $autoFlow &&
    css`
      grid-auto-flow: ${$autoFlow};
    `}
`;
