import { Combobox, InputBox, Text } from '@onyma-ds/react';

export default function OperatorCombobox() {
  return (
    <InputBox label="Selecione a operadora">
      <Combobox.Root>
        <Combobox.Trigger
          placeholder=""
          data-placeholder="false"
        >
          Selecione...
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content placeholder="">
            <Combobox.Command>
              <Combobox.Input
                crossOrigin
                placeholder="Buscar..."
              />
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Item placeholder="">AMIL</Combobox.Item>
                  <Combobox.Item placeholder="">CNU</Combobox.Item>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
      </Combobox.Root>
    </InputBox>
  );
}
