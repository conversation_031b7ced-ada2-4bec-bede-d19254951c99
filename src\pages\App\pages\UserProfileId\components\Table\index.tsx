import { useCallback, useMemo, useState } from 'react';
import { Heading, Switch, Table as TableDS } from '@onyma-ds/react';
import { ButtonExpand } from './components';
import * as SC from './styles';
import { data } from './data';
import { Menu, MenuMapped } from './types';

export default function Table() {
  const [itemsToExpand, setItemsToExpand] = useState<string[]>([]);

  const handleExpand = (id: string) => {
    if (itemsToExpand.includes(id)) {
      return setItemsToExpand((prev) => prev.filter((item) => item !== id));
    }
    setItemsToExpand((prev) => [...prev, id]);
  };

  const reduceArray = useCallback(
    (menus: Menu[], iteration: number) => {
      return menus.reduce((newData, item) => {
        newData.push({ ...item, menuIteration: iteration });
        if (itemsToExpand.includes(item.id)) {
          newData.push(...reduceArray(item.menus, iteration + 1));
        }
        return newData;
      }, [] as MenuMapped[]);
    },
    [itemsToExpand],
  );

  const dataToRender = useMemo(() => {
    return reduceArray(data, 0);
  }, [reduceArray]);

  return (
    <SC.Container>
      <Heading
        as="h4"
        type="heading_04"
      >
        Menus
      </Heading>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={2}
              title="Nome"
            >
              Página
            </TableDS.Th>
            <TableDS.Th
              colSpan={2}
              title="URL"
            >
              URL
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="Posição"
            >
              Empresa
            </TableDS.Th>
            <TableDS.Th>
              <SC.ThActions>
                <Switch />
              </SC.ThActions>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {dataToRender.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td colSpan={2}>
                <SC.TdBox>
                  <SC.MenuTab $span={item.menuIteration} />
                  {item.menus.length > 0 && (
                    <ButtonExpand
                      data-state={
                        itemsToExpand.includes(item.id)
                          ? 'expanded'
                          : 'compressed'
                      }
                      onClick={() => handleExpand(item.id)}
                    />
                  )}
                  {item.name}
                </SC.TdBox>
              </TableDS.Td>
              <TableDS.Td colSpan={2}>
                <SC.LinkUrl
                  to={item.url}
                  target="_blank"
                >
                  {item.url}
                </SC.LinkUrl>
              </TableDS.Td>
              <TableDS.Td colSpan={1}>{item.company}</TableDS.Td>
              <TableDS.Td>
                {typeof item.hasPermition === 'boolean' && (
                  <SC.ThActions>
                    <Switch />
                  </SC.ThActions>
                )}
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
