import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadPowerBIApplicationId } from './types';

export const remoteLoadPowerBIApplicationId: RemoteLoadPowerBIApplicationId =
  async () => {
    const response = await httpClient.post('/configuracao/fetch', null, {
      params: {
        chave: 'PowerBiApplicationId',
      },
    });
    return {
      ...response.data,
      title: formatters.titleCase(response.data.title),
    };
  };
