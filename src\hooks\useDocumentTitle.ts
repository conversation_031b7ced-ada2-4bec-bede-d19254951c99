import { BREADCRUMB_ROUTES } from '@/components/layouts/breadcrumb/routes-data';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface UseDocumentTitleOptions {
  suffix?: string;
  separator?: string;
  customTitle?: string;
}

/**
 * Hook para gerenciar o título do documento de forma dinâmica
 * baseado na rota atual e configuração do breadcrumb
 */
export function useDocumentTitle(options: UseDocumentTitleOptions = {}) {
  const location = useLocation();
  const { suffix = 'Portal BenCorp', separator = ' - ', customTitle } = options;

  useEffect(() => {
    // Se há um título customizado, usa ele
    if (customTitle) {
      document.title = customTitle + separator + suffix;
      return;
    }

    // Função para determinar a rota atual baseada no pathname
    const getCurrentRoute = () => {
      const currentPath = location.pathname;

      for (const [key, route] of Object.entries(BREADCRUMB_ROUTES)) {
        if (route.patterns.some((pattern) => currentPath.includes(pattern))) {
          return { key, ...route };
        }
      }

      return null;
    };

    // Função para obter o nome da página atual (último segmento da URL)
    const getCurrentPageName = () => {
      const segments = location.pathname.split('/').filter(Boolean);
      return segments[segments.length - 1] || '';
    };

    // Função para formatar o nome da página
    const formatPageName = (pageName: string) => {
      if (!pageName) return '';
      const formattedPageName = pageName.split('-').join(' ');
      // capitalize
      return formattedPageName.replace(/\b\w/g, (l) => l.toUpperCase());
    };

    const currentRoute = getCurrentRoute();
    const currentPageName = getCurrentPageName();
    const isMainRoute = currentRoute !== null;

    let pageTitle = '';

    if (isMainRoute && currentRoute) {
      pageTitle = currentRoute.label;

      // Se não estamos exatamente na rota principal, adiciona o nome da sub-página
      const isExactMainRoute = currentRoute.patterns.some(
        (pattern) =>
          location.pathname === currentRoute.path ||
          location.pathname === `/app${pattern}`,
      );

      if (!isExactMainRoute && currentPageName) {
        const formattedPageName = formatPageName(currentPageName);
        if (formattedPageName) {
          pageTitle = `${formattedPageName} - ${currentRoute.label}`;
        }
      }
    } else {
      pageTitle = (formatPageName(currentPageName) as string) || 'Página';
    }

    // Define o título final
    document.title = pageTitle + separator + suffix;
  }, [location.pathname, customTitle, suffix, separator]);

  // Função para definir um título customizado programaticamente
  const setCustomTitle = (title: string) => {
    document.title = title + separator + suffix;
  };

  // Função para resetar para o título automático
  const resetTitle = () => {
    // Força uma re-execução do useEffect removendo e re-adicionando o customTitle
    const event = new CustomEvent('resetDocumentTitle');
    window.dispatchEvent(event);
  };

  return {
    setCustomTitle,
    resetTitle,
  };
}

export function usePageTitle(
  title: string,
  options: Omit<UseDocumentTitleOptions, 'customTitle'> = {},
) {
  return useDocumentTitle({ ...options, customTitle: title });
}
