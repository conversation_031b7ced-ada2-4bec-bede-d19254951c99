/* eslint-disable react-hooks/exhaustive-deps */
import {
  PropsWithChildren,
  createContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useToast } from '@onyma-ds/react';
import { SystemMenu } from '@/services/api/menus/remoteLoadMenus';
import { useApi } from '@/contexts/api';
import { Filters, MenusContextData } from './types';
import { Pagination } from '@/@types/Pagination';
import { helpers } from './helpers';

export const MenusContext = createContext<MenusContextData | null>(null);

export const MenusContextProvider = ({ children }: PropsWithChildren) => {
  const { addToast } = useToast();
  const { menus } = useApi();

  const [loading, setLoading] = useState({ listing: false });
  const [systemMenus, setSystemMenus] = useState<SystemMenu[]>([]);
  const [filters, setFilters] = useState<Filters>({});
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const loadMenus = async () => {
    try {
      setLoading((prev) => ({ ...prev, listing: true }));
      const resultMenus = await menus.loadMenus();
      setSystemMenus(resultMenus.result);
      setPagination((prev) => ({
        ...prev,
        totalItems: resultMenus.result.length,
      }));
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data.title || 'Carregando dos Menus',
        description:
          error.response?.data.message || 'Erro ao carregar os menus',
        timeout: 5000,
      });
    } finally {
      setLoading((prev) => ({ ...prev, listing: false }));
    }
  };

  useEffect(() => {
    loadMenus();
  }, []);

  const systemMenusMapped = useMemo(() => {
    return helpers.applyPagination(
      helpers.applyFilters(systemMenus, filters),
      pagination,
    );
  }, [systemMenus, filters, pagination]);

  return (
    <MenusContext.Provider
      value={{
        loading,
        data: systemMenusMapped,
        filters,
        pagination,
        refetch: loadMenus,
        onFiltersChange: setFilters,
        onPaginationChange: setPagination,
      }}
    >
      {children}
    </MenusContext.Provider>
  );
};
