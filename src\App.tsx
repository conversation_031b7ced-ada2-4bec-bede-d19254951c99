import * as React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ThemeProvider } from 'styled-components';
import { RouterProvider } from 'react-router-dom';
import { ToastProvider } from '@onyma-ds/react';
import { AuthContextProvider } from './contexts/auth';
import { CompanyContextProvider } from './contexts/company';
import { ApiContextProvider } from './contexts/api';
import { MenuSideBarContextProvider } from './contexts/menuSideBar';
import './styles.css';
import { GlobalStyles } from './styles/GlobalStyles';
import { theme } from '@/styles/theme';
import { router } from './router';

const queryClient = new QueryClient();

const ReactQueryDevtoolsProduction = React.lazy(() =>
  import('@tanstack/react-query-devtools/production').then((d) => ({
    default: d.ReactQueryDevtools,
  })),
);

export default function App() {
  const [showDevtools, setShowDevtools] = React.useState(false);

  React.useEffect(() => {
    // @ts-expect-error: add `toggleDevtools` method to the window object
    window.toggleDevtools = () => setShowDevtools((prev) => !prev);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ApiContextProvider>
        <ToastProvider>
          <MenuSideBarContextProvider>
            <AuthContextProvider>
              <CompanyContextProvider>
                <ThemeProvider theme={theme}>
                  <GlobalStyles />
                  <RouterProvider router={router} />
                  <ReactQueryDevtools initialIsOpen />
                  {showDevtools && (
                    <React.Suspense fallback={null}>
                      <ReactQueryDevtoolsProduction />
                    </React.Suspense>
                  )}
                </ThemeProvider>
              </CompanyContextProvider>
            </AuthContextProvider>
          </MenuSideBarContextProvider>
        </ToastProvider>
      </ApiContextProvider>
    </QueryClientProvider>
  );
}
