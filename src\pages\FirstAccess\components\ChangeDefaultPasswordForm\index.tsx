import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ChangeDefaultPasswordFormProps } from './types';
import {
  changeDefaultPasswordFormSchema,
  ChangeDefaultPasswordFormValues,
} from './validation';

export default function ChangeDefaultPasswordForm({
  onSuccess,
}: ChangeDefaultPasswordFormProps) {
  const { user } = useAuth();
  const { auth } = useApi();
  const {
    formState: { errors, isSubmitting },
    register,
    setError,
    clearErrors,
    handleSubmit,
  } = useForm<ChangeDefaultPasswordFormValues>({
    resolver: zodResolver(changeDefaultPasswordFormSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit: SubmitHandler<ChangeDefaultPasswordFormValues> = async (
    data,
  ) => {
    try {
      clearErrors('root');
      await auth.changePassword({
        userId: user.uuid,
        password: data.password,
        passwordRepeat: data.confirmPassword,
        checkCurrent: false,
      });
      onSuccess();
    } catch (error) {
      setError('root', { type: 'custom', message: error.message });
    }
  };

  return (
    <form
      className="flex flex-col gap-12 w-[360px] items-center"
      onSubmit={handleSubmit(onSubmit)}
    >
      <h1 className="text-center text-2xl font-semibold">
        Defina uma nova senha
      </h1>

      <div className="flex flex-col gap-8 w-full">
        <Input
          label="Nova senha"
          type="password"
          id="password"
          placeholder="Digite a nova senha"
          errorMessage={errors.password?.message}
          {...register('password')}
        />

        <Input
          label="Confirmação da nova senha"
          type="password"
          id="confirm-password"
          placeholder="Digite a nova senha"
          errorMessage={errors.confirmPassword?.message}
          {...register('confirmPassword')}
        />

        {errors.root && (
          <span
            className="text-red-500 text-sm"
            role="alert"
          >
            {errors.root.message}
          </span>
        )}
      </div>

      <Button
        className="w-full"
        type="submit"
        disabled={!!errors.password || !!errors.confirmPassword || isSubmitting}
        isLoading={isSubmitting}
      >
        Alterar Senha
      </Button>
    </form>
  );
}
