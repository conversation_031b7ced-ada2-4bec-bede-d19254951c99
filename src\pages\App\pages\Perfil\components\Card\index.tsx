import { Heading } from '@onyma-ds/react';
import * as SC from './styles';
import { Props } from './types';

export default function Card({ title, children }: Props) {
  return (
    <SC.Container>
      <SC.Header>
        <Heading
          as="h5"
          type="heading_05"
        >
          {title}
        </Heading>
      </SC.Header>
      <SC.Content>{children}</SC.Content>
    </SC.Container>
  );
}
