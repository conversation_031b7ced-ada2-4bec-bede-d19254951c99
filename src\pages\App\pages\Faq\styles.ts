import styled from 'styled-components';

export const Container = styled.main`
  padding: 1rem 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  h1 {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.25;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    padding: 2rem 1.75rem;
  }
`;

export const AccordionWrapper = styled.div`
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
`;

export const ChildrenWrapper = styled.div`
  .title-summary {
    color: ${({ theme }) => theme.colors.black};
  }
  margin-bottom: 1rem;
`;
