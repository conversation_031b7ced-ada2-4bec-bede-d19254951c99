import { Icons } from '@/components';
import { PageHeader } from '../../components';
import * as SC from './styles';
import UserGroup from './UserGroup';

export default function UserGroupPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Grupo de usuários</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>

      <UserGroup />
    </SC.Container>
  );
}
