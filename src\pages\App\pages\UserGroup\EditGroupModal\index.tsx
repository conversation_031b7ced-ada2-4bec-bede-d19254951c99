import { Dialog } from '@onyma-ds/react';
import EditUserGroupForm from './Form';
import { EditGroupModalProps } from './types';

import * as SC from './styles';

export default function EditGroupModal({
  isOpen,
  setModalState,
  defaultValues,
}: EditGroupModalProps) {
  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">Editar grupo de usuários</Dialog.Title>
          </Dialog.Header>
          <Dialog.Body>
            <EditUserGroupForm
              defaultValues={defaultValues}
              onClose={setModalState}
            />
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
