import { httpClient } from '@/services/httpClient';
import { RemoteUpdateClientCompany } from './types';

export const remoteUpdateClientCompany: RemoteUpdateClientCompany = async (
  params,
) => {
  const response = await httpClient.post('/customercompany/update', {
    id: params.id,
    nome: params.name,
    inclusoBenMaisSaude: params.includedBenSaude,
    ativo: params.isActive,
    codigoSoc: params.codigoSoc,
    logo: params.logo,
  });
  return response.data;
};
