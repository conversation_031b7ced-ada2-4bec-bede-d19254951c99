import axios from 'axios';
import { AuthStorageService } from './storage/AuthStorageService';

export const httpClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 30000,
  headers: {
    'X-Api-Key': import.meta.env.VITE_API_KEY,
  },
});

export const httpClientPsicossocial = axios.create({
  baseURL: import.meta.env.VITE_API_URL_PSICOSSOCIAL,
  timeout: 30000,
  headers: {
    // 'X-Api-Key': import.meta.env.VITE_API_KEY
  },
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 403) {
      AuthStorageService.clear();
      alert('Seu token expirou. Por favor, faça login novamente.');
      window.location.replace('/login');
      throw new Error(
        'O Token de acesso ao PowerBI expirou. Por favor, faça login novamente.',
      );
    }
    Promise.reject(error);
  },
);
