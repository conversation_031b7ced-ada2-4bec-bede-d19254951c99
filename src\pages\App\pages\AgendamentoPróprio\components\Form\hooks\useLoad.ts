import { useCallback, useEffect, useMemo, useState } from 'react';
import { useToast } from '@onyma-ds/react';
import { useAuth } from '@/contexts/auth';
import { useApi } from '@/contexts/api';
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';
import { rolesId } from '@/utils/constants';

export const useLoad = () => {
  const { addToast } = useToast();
  const { user } = useAuth();
  const { clients } = useApi();

  const [messageToShow, setMessageToShow] = useState('');
  const [companies, setCompanies] = useState<ClientCompany[]>([]);

  const loadCompanies = useCallback(async () => {
    try {
      const resultCompanies = await clients.loadClientCompanies();
      const allActiveCompanies = resultCompanies.result.filter(
        (company) => company.isActive,
      );
      if (!user.companyId) {
        setCompanies([]);
        return setMessageToShow(
          'Você precisa estar associado a uma empresa dentro do portal. Solicite ao seu gestor para cadastrar o seu usuário.',
        );
      }
      if (user.currentRole.id === rolesId.administradorDoSistema) {
        return setCompanies(allActiveCompanies);
      }
      setCompanies(
        allActiveCompanies.filter((company) => company.id === user.companyId),
      );
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data.title || 'Carregamento das empresas',
        description:
          error.response?.data.message || 'Erro ao carregar as empresas',
        timeout: 5000,
      });
    }
  }, [addToast, clients, user]);

  const companiesOptions = useMemo(() => {
    const optionsMapped = companies.map((company) => ({
      label: company.name,
      value: company.id,
    }));
    return optionsMapped;
  }, [companies]);

  useEffect(() => {
    Promise.all([loadCompanies()]);
  }, [loadCompanies]);

  return { messageToShow, companiesOptions };
};
