import { httpClient } from '@/services/httpClient';
import { IremoteEditFile } from './types';

export const remoteEditFile: IremoteEditFile = async (params) => {
  const formData = new FormData();
  const profiles = params.perfil?.split(',');

  for (const [key, value] of Object.entries(params)) {
    if (key !== 'arquivo' && key !== 'perfil') {
      formData.append(key, value as string);
    }
  }

  formData.append('arquivo', params.arquivo);
  profiles?.map((profile) => {
    formData.append('perfil', profile);
  });

  const response = await httpClient.post('/arquivos/update', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};
