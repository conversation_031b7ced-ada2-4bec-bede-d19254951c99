import styled from 'styled-components';

export const Container = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
`;

export const TitleBox = styled.div`
  color: ${({ theme }) => theme.colors.secondary};
  display: flex;
  align-items: center;
  gap: 0.5rem;

  h3 {
    color: ${({ theme }) => theme.colors.black};
  }
`;

export const BackButton = styled.button`
  background-color: transparent;
  color: ${({ theme }) => theme.colors.secondary};
  border: none;
  transition: color 0.2s;
  cursor: pointer;

  &:hover {
    color: ${({ theme }) => theme.colors.secondary_dark};
  }
`;
