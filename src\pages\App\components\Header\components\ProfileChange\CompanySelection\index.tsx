/* eslint-disable react-hooks/exhaustive-deps */
import { AuthRole } from '@/@types/AuthRole';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { Button, InputBox, SelectBox, useToast } from '@onyma-ds/react';
import { useMutation } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import SelectCompany from '../CompanyCheckCombobox';
import { Container, WrapperActionsButton } from './styles';
import { Option, Props } from './types';

export default function CompanySelection({ onClose }: Props) {
  const { user, setUserData } = useAuth();
  const { setCompanyData } = useCompany();
  const {
    clients: { loadClientCompanies, loadClientCompany, updateUserRole },
  } = useApi();
  const { addToast } = useToast();
  const [selectedProfile, setSelectedProfile] = useState<Option | null>(null);
  const [selectedCompany, setSelectedCompany] = useState<Option | null>(null);
  const [allCompanies, setAllCompanies] = useState<Option[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const handleChangeProfile = (value: Option) => {
    const selectedRole = user.roles.find((role) => role.id === value.value);
    setSelectedProfile({
      value: selectedRole?.id ?? '',
      label: selectedRole?.description ?? '',
      requiresCompany: selectedRole?.requiresCompany,
    });
  };

  const getAllCompanies = useCallback(async () => {
    try {
      setLoading(true);
      const allCompaniesResponse = await loadClientCompanies();

      const allActiveCompanies = allCompaniesResponse.result.filter(
        (company) => company.isActive,
      );

      setAllCompanies(
        allActiveCompanies
          .map((company) => {
            return {
              value: company.id,
              label: company.name,
              logo: company.logo,
            };
          })
          .sort((a, b) => a.label.localeCompare(b.label)),
      );

      if (user.companyId && user.companyName) {
        setSelectedCompany({
          value: user.companyId,
          label: user.companyName,
        });
      } else {
        const currentCompany = allCompaniesResponse.result.find(
          (company) => company.id === user.companyId,
        );
        setSelectedCompany({
          value: currentCompany?.id ?? '',
          label: currentCompany?.name ?? '',
        });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Erro ao buscar as empresas.',
        description:
          'Algo aconteceu ao buscar empresas. Tente novamente mais tarde.',
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    setSelectedProfile({
      value: user.currentRole.id,
      label: user.currentRole.description,
      requiresCompany: user.currentRole.requiresCompany,
    });

    getAllCompanies();
  }, [user]);

  const { mutateAsync } = useMutation({
    mutationFn: (data: { company: string | null; role: string; id: string }) =>
      updateUserRole({ company: data.company, role: data.role, uuid: data.id }),
    onSuccess: async () => {
      if (selectedProfile?.requiresCompany) {
        const response = await loadClientCompany(
          selectedCompany?.value as string,
        );

        setCompanyData(response.result);
      }

      const updatedUser = {
        ...user,
        companyId:
          selectedProfile?.requiresCompany && !!selectedCompany
            ? selectedCompany?.value
            : null,
        companyName:
          selectedProfile?.requiresCompany && !!selectedCompany
            ? selectedCompany?.label
            : null,
        currentRole: user.roles.find(
          (role) => role.id === selectedProfile?.value,
        ) as AuthRole,
      };

      setUserData(updatedUser);
      addToast({
        type: 'success',
        title: 'Perfil alterado com sucesso.',
        description: 'Seu perfil e/ou empresa foram alterados com sucesso.',
        timeout: 5000,
      });
      setSelectedProfile(null);
      setSelectedCompany(null);
      window.location.replace('/app/home');
      onClose();
    },
    onError: () => {
      addToast({
        type: 'error',
        title: 'Erro ao tentar trocar perfil.',
        description:
          'Algo aconteceu ao fazer a troca de perfil. Tente novamente mais tarde.',
        timeout: 5000,
      });
    },
  });

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!selectedProfile) return;

    if (selectedProfile.requiresCompany && !selectedCompany) {
      addToast({
        type: 'error',
        title: 'Empresa não selecionada.',
        description: 'Este perfil requer que você adicione uma empresa.',
      });
      return;
    }

    await mutateAsync({
      company: selectedCompany?.value ?? null,
      role: selectedProfile.value,
      id: user.uuid,
    });
  };

  const hasRoleAdmin = user.roles.some((role) => role.name.includes('Admin'));

  return (
    <Container onSubmit={handleSubmit}>
      <SelectBox
        disabled={loading}
        label="Perfil"
        placeholder="Selecione a empresa"
        optionSelected={selectedProfile}
        onSelect={(option) => {
          setSelectedCompany(null);
          handleChangeProfile(option);
        }}
        options={user.roles.map((role) => ({
          label: role.description,
          value: role.id,
        }))}
      />
      {!!selectedProfile?.requiresCompany && (
        <InputBox label="Empresa">
          <SelectCompany
            company={
              hasRoleAdmin
                ? allCompanies
                : user.companies.map((company) => {
                    return {
                      label: company.nome,
                      value: company.id,
                    };
                  })
            }
            selectedCompany={selectedCompany}
            setSelectedCompany={setSelectedCompany}
          />
        </InputBox>
      )}

      <WrapperActionsButton>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="secondary"
          type="submit"
          color="white"
        >
          Salvar
        </Button>
      </WrapperActionsButton>
    </Container>
  );
}
