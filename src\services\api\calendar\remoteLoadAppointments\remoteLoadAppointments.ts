import { isAxiosError } from 'axios';
import { httpClient } from '@/services/httpClient';
import { soc } from '@/utils/constants';
import { Appointment, RemoteLoadAppointments } from './types';

export const remoteLoadAppointments: RemoteLoadAppointments = async ({
  empresa = soc.company.code,
  codigo = soc.appointments.code,
  chave = soc.appointments.key,
  tipoSaida = soc.outputType.json,
  dataInicial,
  dataFinal,
  codigoUsuarioAgenda,
  codigoEmpresaBusca,
  matriculaFuncionarioBusca,
  ...params
}) => {
  try {
    const searchParams = new URLSearchParams({
      parametro: JSON.stringify({
        empresa: empresa,
        codigo: codigo,
        chave: chave,
        tipoSaida: tipoSaida,
        dataInicial: dataInicial.toLocaleDateString('pt-BR'),
        dataFinal: dataFinal.toLocaleDateString('pt-BR'),
        codigoUsuarioAgenda: codigoUsuarioAgenda,
        codigoEmpresaBusca: codigoEmpresaBusca,
        matriculaFuncionarioBusca: matriculaFuncionarioBusca,
        ...params,
      }),
    });
    const response = await httpClient.get<Appointment[]>('/soc/exportardados', {
      params: {
        parametros: searchParams,
      },
    });
    return {
      statusCode: response.status,
      title: 'Pesquisa realizada',
      message: 'A busca foi realizada com sucesso',
      errorType: null,
      hasError: false,
      result: response.data,
    };
  } catch (error) {
    if (isAxiosError(error)) {
      return {
        statusCode: error.status ?? 400,
        title: error.name,
        message: error.message,
        errorType: 'axios-error',
        hasError: true,
        result: [],
      };
    } else {
      return {
        statusCode: 500,
        title: 'Erro ao realizar pesquisa',
        message: error.message ?? 'Erro desconhecido',
        errorType: 'default-error',
        hasError: true,
        result: [],
      };
    }
  }
};
