import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadPowerBITenant } from './types';

export const remoteLoadPowerBITenant: RemoteLoadPowerBITenant = async () => {
  const response = await httpClient.post('/configuracao/fetch', null, {
    params: {
      chave: 'PowerBiTenant',
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
  };
};
