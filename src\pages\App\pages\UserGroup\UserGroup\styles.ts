import styled from 'styled-components';

export const Container = styled.section`
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
`;

export const ContainerLoading = styled.section`
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const QuickFilterBox = styled.section`
  width: 200px;
  margin-left: auto;
  margin-bottom: 1.5rem;
`;

export const TableBox = styled.div`
  overflow: auto;
  position: relative;
`;

export const PaginationBox = styled.section`
  margin: 0 auto;

  & > div {
    flex-wrap: wrap;
  }
`;
