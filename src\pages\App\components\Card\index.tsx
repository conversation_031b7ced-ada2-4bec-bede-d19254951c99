import { Heading, Text } from '@onyma-ds/react';
import * as SC from './styles';
import { Props } from './types';

export default function Card({
  imgSrc,
  imgAlt,
  title,
  description,
  linkTo,
  targetBlank,
}: Props) {
  return (
    <SC.Container>
      <SC.ImgBox>
        <img
          src={imgSrc}
          alt={imgAlt}
          loading="lazy"
        />
      </SC.ImgBox>
      <div className="container-wrapper">
        <Heading
          as="h4"
          type="heading_04"
        >
          {title}
        </Heading>

        {description ? (
          <Text
            className="text-overflow"
            title={description}
          >
            {description}
          </Text>
        ) : (
          <Text
            className="text-overflow"
            title={description}
          >
            {''}
          </Text>
        )}
        <SC.AccessLink
          to={linkTo}
          target={targetBlank ? '_blank' : '_self'}
        >
          Acessar
        </SC.AccessLink>
      </div>
    </SC.Container>
  );
}
