import { Icons } from '@/components';
import { useAuth } from '@/contexts/auth';
import { Button, Dialog } from '@onyma-ds/react';
import { useState } from 'react';
import CompanySelection from './CompanySelection';
import * as SC from './styles';

export default function ProfileChange() {
  const { user } = useAuth();
  const [show, setShow] = useState(false);

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>
        <SC.TriggerContainer>
          <Button
            variant="secondary"
            buttonType="secondary"
          >
            Editar perfil: {user.currentRole.name}{' '}
            {user.currentRole.requiresCompany && ` - ${user.companyName ?? ''}`}
            <Icons.RAIo5.IoChevronDown size={20} />
          </Button>
        </SC.TriggerContainer>
      </Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">Trocar de perfil</Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <CompanySelection onClose={() => setShow((state) => !state)} />
            </Dialog.Body>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
