import { Button } from '@/components/ui/button';
import { Combobox } from '@/components/ui/combobox';
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SelectField } from '@/components/ui/select';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@onyma-ds/react';
import { useCallback, useEffect, useState } from 'react';
import { AuthRole } from '@/@types/AuthRole';
import { useNavigate } from 'react-router-dom';

const profileValidation = z.object({
  profile: z.object(
    {
      label: z.string(),
      value: z.string(),
      requiresCompany: z.boolean().optional(),
    },
    { message: 'Campo obrigatório' },
  ),
  company: z
    .object(
      {
        label: z.string(),
        value: z.string(),
      },
      { message: 'Campo obrigatório' },
    )
    .optional(),
});

type ProfileValidationType = z.infer<typeof profileValidation>;

type Option = {
  label: string;
  value: string;
  requiresCompany?: boolean;
  logo?: string | null;
};

export function ProfileChange() {
  const { user, setUserData } = useAuth();
  const { setCompanyData } = useCompany();
  const { addToast } = useToast();
  const navigate = useNavigate();
  const {
    clients: { loadClientCompanies, loadClientCompany, updateUserRole },
  } = useApi();

  const [selectedProfile, setSelectedProfile] = useState<Option | null>(null);
  const [selectedCompany, setSelectedCompany] = useState<Option | null>(null);
  const [allCompanies, setAllCompanies] = useState<Option[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    setError,
    watch,
    clearErrors,
    formState: { errors },
  } = useForm<ProfileValidationType>({
    resolver: zodResolver(profileValidation),
    defaultValues: {
      profile: { label: '', value: '' },
      company: { label: '', value: '' },
    },
  });

  const handleChangeProfile = (value: { label: string; value: string }) => {
    const selectedRole = user.roles.find((role) => role.id === value.value);
    const profileOption = {
      value: selectedRole?.id ?? '',
      label: selectedRole?.description ?? '',
      requiresCompany: selectedRole?.requiresCompany,
    };
    setSelectedProfile(profileOption);
    setValue('profile', value);
  };

  const getAllCompanies = useCallback(async () => {
    try {
      setLoading(true);
      const allCompaniesResponse = await loadClientCompanies();

      const allActiveCompanies = allCompaniesResponse.result.filter(
        (company) => company.isActive,
      );

      const companiesOptions = allActiveCompanies
        .map((company) => ({
          value: company.id,
          label: company.name,
          logo: company.logo,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));

      setAllCompanies(companiesOptions);

      if (user.companyId && user.companyName) {
        const currentCompany = {
          value: user.companyId,
          label: user.companyName,
        };
        setSelectedCompany(currentCompany);
        setValue('company', currentCompany);
      } else {
        const currentCompany = allCompaniesResponse.result.find(
          (company) => company.id === user.companyId,
        );
        if (currentCompany) {
          const companyOption = {
            value: currentCompany.id,
            label: currentCompany.name,
          };
          setSelectedCompany(companyOption);
          setValue('company', companyOption);
        }
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Erro ao buscar as empresas.',
        description:
          'Algo aconteceu ao buscar empresas. Tente novamente mais tarde.',
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  }, [user, loadClientCompanies, addToast, setValue]);

  useEffect(() => {
    const currentProfile = {
      value: user.currentRole.id,
      label: user.currentRole.description,
      requiresCompany: user.currentRole.requiresCompany,
    };
    setSelectedProfile(currentProfile);
    setValue(
      'profile',
      {
        value: user.currentRole.id,
        label: user.currentRole.name,
        requiresCompany: user.currentRole.requiresCompany,
      },
      { shouldValidate: true },
    );

    getAllCompanies();
  }, [user, getAllCompanies, setValue]);

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: { company: string | null; role: string; id: string }) =>
      updateUserRole({ company: data.company, role: data.role, uuid: data.id }),
    onSuccess: async () => {
      if (selectedProfile?.requiresCompany && selectedCompany) {
        const response = await loadClientCompany(selectedCompany.value);
        setCompanyData(response.result);
      }

      const updatedUser = {
        ...user,
        companyId:
          selectedProfile?.requiresCompany && selectedCompany
            ? selectedCompany.value
            : null,
        companyName:
          selectedProfile?.requiresCompany && selectedCompany
            ? selectedCompany.label
            : null,
        currentRole: user.roles.find(
          (role) => role.id === selectedProfile?.value,
        ) as AuthRole,
      };

      setUserData(updatedUser);
      addToast({
        type: 'success',
        title: 'Perfil alterado com sucesso.',
        description: 'Seu perfil e/ou empresa foram alterados com sucesso.',
        timeout: 5000,
      });
      setSelectedProfile(null);
      setSelectedCompany(null);
      reset();
      setOpen(false);
      navigate('/app/home');
      window.location.reload();
    },
    onError: () => {
      addToast({
        type: 'error',
        title: 'Erro ao tentar trocar perfil.',
        description:
          'Algo aconteceu ao fazer a troca de perfil. Tente novamente mais tarde.',
        timeout: 5000,
      });
    },
  });

  const handleSubmitForm = async (data: ProfileValidationType) => {
    if (!selectedProfile) return;

    if (selectedProfile.requiresCompany && !data.company?.value) {
      setError('company', {
        message: 'Este perfil requer que você adicione uma empresa.',
      });
      return;
    }

    await mutateAsync({
      company: data.company?.value ?? null,
      role: data.profile.value,
      id: user.uuid,
    });
  };

  const hasRoleAdmin = user.roles.some((role) => role.name.includes('Admin'));

  const getCompanyOptions = () => {
    if (hasRoleAdmin) {
      return allCompanies;
    }
    return user.companies.map((company) => ({
      label: company.nome,
      value: company.id,
    }));
  };

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
      modal
    >
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="rounded-lg"
        >
          {user.currentRole.name}{' '}
          {user.currentRole.requiresCompany && ` - ${user.companyName ?? ''}`}
          <ChevronDown />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[480px] p-8">
        <DialogHeader className="flex flex-col items-center mb-8">
          <DialogTitle>
            <span className="text-2xl">Trocar perfil/empresa</span>
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleSubmitForm)}>
          <div className="flex flex-col gap-10">
            <Controller
              name="profile"
              control={control}
              render={({ field }) => (
                <SelectField
                  label="Perfil"
                  placeholder="Selecione um perfil"
                  disabled={loading || isPending}
                  defaultValue={{
                    label: user.currentRole.description,
                    value: user.currentRole.id,
                  }}
                  options={user.roles.map((role) => ({
                    label: role.description,
                    value: role.id,
                    requiresCompany: role?.requiresCompany,
                  }))}
                  onValueChange={(value) => {
                    setValue('company', { label: '', value: '' });
                    handleChangeProfile(value);
                    field.onChange(value);
                  }}
                  errorMessage={errors.profile?.message}
                />
              )}
            />

            {watch('profile.requiresCompany') &&
              selectedProfile?.requiresCompany && (
                <Controller
                  name="company"
                  control={control}
                  render={({ field }) => (
                    <Combobox
                      disabled={loading || isPending}
                      label="Empresa"
                      options={getCompanyOptions()}
                      onChange={(value) => {
                        setSelectedCompany(value);
                        field.onChange(value);
                      }}
                      value={field.value}
                      errorMessage={errors.company?.message}
                      placeholder="Selecione uma empresa"
                    />
                  )}
                />
              )}

            <div className="flex w-full justify-between gap-8">
              <DialogClose asChild>
                <Button
                  onClick={() => clearErrors()}
                  variant="outline"
                  className="flex-1 h-10"
                  type="button"
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1 h-10"
                isLoading={isPending}
                disabled={loading}
              >
                Salvar
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
