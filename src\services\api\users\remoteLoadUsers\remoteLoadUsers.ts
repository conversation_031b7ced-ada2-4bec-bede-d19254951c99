/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadUsers } from './types';

export const remoteLoadUsers: RemoteLoadUsers = async () => {
  const response = await httpClient.get('/user/fetchall');
  return {
    ...response.data,
    result: response.data.result.map((user: any) => ({
      ...user,
      dataCadastroDate: formatters.datePTBRToType(user.dataCadastro),
      dataUltimoLoginDate: user.dataUltimoLogin
        ? formatters.datePTBRToType(user.dataUltimoLogin)
        : user.dataUltimoLogin,
    })),
  };
};
