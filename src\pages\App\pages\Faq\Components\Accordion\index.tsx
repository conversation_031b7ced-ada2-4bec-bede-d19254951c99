import React, { useCallback, useRef, useState } from 'react';
import { Chevron, Container, ContentWrapper, Title } from './styles';

interface AccordionProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  children: React.ReactNode;
  titleStyle?: React.CSSProperties;
}

export default function Accordion({
  title,
  children,
  ...props
}: AccordionProps) {
  const [isExpanded, setExpand] = useState<boolean>();

  const contentRef = useRef<HTMLDivElement>(null);
  const contentHeight = isExpanded ? 1200 : 0;

  const handleExpandToggle = useCallback(() => {
    setExpand(!isExpanded);
  }, [isExpanded]);

  return (
    <Container {...props}>
      <Title
        onClick={handleExpandToggle}
        className="title-summary"
      >
        {title}
        <Chevron direction={isExpanded ? 'top' : 'bottom'} />
      </Title>
      <ContentWrapper $maxHeight={contentHeight as number}>
        <div ref={contentRef}>{children}</div>
      </ContentWrapper>

      <hr />
    </Container>
  );
}
