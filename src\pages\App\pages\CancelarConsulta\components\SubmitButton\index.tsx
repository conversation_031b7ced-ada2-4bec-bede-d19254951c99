import { Button } from '@onyma-ds/react';
import { Spinner } from '@/components';
import * as SC from './styles';

type SubmitButtonProps = Omit<React.ComponentProps<typeof Button>, 'type'> & {
  isSubmitting?: boolean;
};

export default function SubmitButton({
  isSubmitting,
  children,
  ...props
}: SubmitButtonProps) {
  return (
    <SC.SubmitButton
      type="submit"
      $isSubmitting={isSubmitting}
      {...props}
    >
      {children}
      {isSubmitting && (
        <Spinner
          size={16}
          color="white"
        />
      )}
    </SC.SubmitButton>
  );
}
