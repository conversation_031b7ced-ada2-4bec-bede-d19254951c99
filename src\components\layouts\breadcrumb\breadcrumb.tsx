import { useLocation, useNavigate } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Breadcrumb<PERSON>ist,
  BreadcrumbSeparator,
} from '../../ui/breadcrumb';
import { BREADCRUMB_ROUTES } from './routes-data';
import { titleCase } from '@/utils/formatters/titleCase';

export function BreadcrumbMenu() {
  const location = useLocation();
  const navigate = useNavigate();

  // Função para determinar a rota atual baseada no pathname
  const getCurrentRoute = () => {
    const currentPath = location.pathname;

    for (const [key, route] of Object.entries(BREADCRUMB_ROUTES)) {
      if (route.patterns.some((pattern) => currentPath.includes(pattern))) {
        return { key, ...route };
      }
    }

    return null;
  };

  const getCurrentPageName = () => {
    return location.pathname.split('/').pop() || '';
  };

  const currentRoute = getCurrentRoute();
  const isMainRoute = currentRoute !== null;
  const currentPageName = getCurrentPageName();

  const handleNavigateToMain = () => {
    const targetPath = currentRoute?.path || '/app/home';
    navigate(targetPath);
  };

  const handleFormatRouteText = (pageName: string) => {
    const formatedPageName = pageName.split('-').join(' ');
    return titleCase(formatedPageName);
  };

  return (
    <div className="w-full h-16 bg-teal-300/10 px-6 flex items-center shadow-sm">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem className="cursor-pointer">
            <BreadcrumbLink onClick={handleNavigateToMain}>
              {currentRoute?.label || 'Voltar'}
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbSeparator />

          <BreadcrumbItem>
            <BreadcrumbLink>
              {isMainRoute ? '' : handleFormatRouteText(currentPageName)}
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
