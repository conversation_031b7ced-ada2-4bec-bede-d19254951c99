import { httpClient } from '@/services/httpClient';
import { RemoteUpdateUser } from './types';

export const remoteUpdateUser: RemoteUpdateUser = async (params) => {
  const response = await httpClient.post('/user/update', {
    id: params.id,
    nome: params.nome,
    email: params.email,
    senha: params.senha,
    empresas: params.empresas || null,
    perfis: params.perfis,
    ativo: params.ativo,
    matriculaSoc: params.matriculaSoc || null,
    codigoSoc: params.codigoSoc || null,
  });
  return response.data;
};
