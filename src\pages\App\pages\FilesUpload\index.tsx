import { Icons } from '@/components';
import { PageHeader } from '../../components';
import * as SC from './styles';
import FilesUploadPage from './Page';

export default function FilesUpload() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Upload de arquivos</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>

      <FilesUploadPage />
    </SC.Container>
  );
}
