import { Heading } from '@onyma-ds/react';
import { ButtonRequestAccess } from '..';
import * as SC from './styles';

export default function Hero() {
  return (
    <SC.Container>
      <SC.Titles>
        <Heading
          as="h1"
          type="heading_01"
        >
          Tenha acesso a indicadores, suporte e treinamentos em um só lugar.
        </Heading>
        <Heading
          as="h4"
          type="heading_04"
        >
          Veja como é fácil acompanhar seus indicadores de sucesso e gerenciar
          suas solicitações diárias.
        </Heading>
        <ButtonRequestAccess />
      </SC.Titles>
      <SC.ImageBox>
        <img
          src="/imgs/manager.png"
          alt="Imagem de uma pessoa mexendo no notebook"
        />
      </SC.ImageBox>
    </SC.Container>
  );
}
