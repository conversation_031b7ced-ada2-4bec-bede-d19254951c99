import * as z from 'zod';

const requiredMessage = 'Esse campo é obrigatório';

export const uploadFileSchema = z.object({
  file: z
    .instanceof(File, { message: requiredMessage })
    .refine((file) => !!file, { message: 'O arquivo é obrigatório.' })
    .refine(
      (file) =>
        [
          'application/pdf',
          'application/vnd.ms-excel', // Tipo MIME para arquivos .xls
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // Tipo MIME para arquivos .xlsx
        ].includes(file.type),
      {
        message:
          'O arquivo deve ser uma imagem (JPEG/PNG), PDF ou Excel (XLS/XLSX).',
      },
    ),
  fileName: z
    .string({ message: requiredMessage })
    .min(1, { message: requiredMessage }),
  fileType: z
    .object(
      {
        label: z.string(),
        value: z.string(),
      },
      { message: requiredMessage },
    )
    .optional(),
  company: z
    .object(
      {
        label: z.string(),
        value: z.string(),
      },
      { message: requiredMessage },
    )
    .optional(),
  userGroup: z
    .string({ message: requiredMessage })
    .min(1, { message: requiredMessage })
    .optional(),
});

export type UploadFileSchemaType = z.infer<typeof uploadFileSchema>;
