import { Icons } from '@/components';
import { PageHeader } from '../../components';
import { Menus } from './components';
import { MenusContextProvider } from './contexts/menus';

export default function SystemMenusPage() {
  return (
    <div className="h-full p-8 flex flex-col gap-6">
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Sistema</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <MenusContextProvider>
        <Menus />
      </MenusContextProvider>
    </div>
  );
}
