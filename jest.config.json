{"roots": ["<rootDir>/src"], "preset": "ts-jest", "clearMocks": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{ts,tsx}", "!**/*.d.ts"], "coverageDirectory": "coverage", "coverageProvider": "v8", "testEnvironment": "jest-environment-jsdom", "transformIgnorePatterns": ["<rootDir>/node_modules/"], "testPathIgnorePatterns": ["<rootDir>/node_modules/"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts", "<rootDir>/node_modules/jest-offline"], "transform": {"^.+\\.(t|j)sx?$": ["@swc/jest", {"jsc": {"transform": {"react": {"runtime": "automatic"}}}}], "^.+\\.svg$": "jest-transformer-svg"}, "moduleNameMapper": {"@/(.*)": "<rootDir>/src/$1", "@/tests/(.*)": "<rootDir>/tests/$1"}}