import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';
import { LinkSentMessageProps } from './types';

export default function LinkSentMessage({ email }: LinkSentMessageProps) {
  return (
    <div className="flex flex-col gap-10 w-[400px]">
      <h2 className="text-center text-2xl font-semibold">
        Se existir uma conta com o e-mail{' '}
        <span className="text-primary">{email}</span>, você receberá um link
        para a redefinição da senha.
      </h2>
      <Link to="/login">
        <Button
          type="button"
          className="w-full"
        >
          Acessar minha conta
        </Button>
      </Link>
    </div>
  );
}
