import { z } from 'zod';

const required_error = 'Campo obrigatório';

export const AgendamentoSchema = z.object({
  matriculaColaborador: z.coerce
    .string({ required_error })
    .min(1, { message: required_error }),
  nomeColaborador: z.coerce
    .string({ required_error })
    .min(1, { message: required_error }),
  dataAgendamento: z.date({ required_error }),
  emailPessoal: z
    .string({ message: required_error })
    .email({ message: 'E-mail inválido' }),
  hour: z.object(
    {
      label: z.string({ required_error }),
      value: z.string({ required_error }),
    },
    { required_error },
  ),
});

export type AgendamentoSchemaType = z.infer<typeof AgendamentoSchema>;
