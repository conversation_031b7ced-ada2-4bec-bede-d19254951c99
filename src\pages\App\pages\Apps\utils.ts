import { Menu } from '@/services/api/menus/remoteLoadMenus';

export const handleCardClick = (menu: Menu) => {
  if (menu.type === 1) {
    return `/app/iframe?url=${menu.url}`;
  }
  if (menu.type === 2) {
    return `/app/bi/${menu.id ?? menu.uuid}`;
  }
  if (menu.type === 5) {
    return `/app/submenu?id=${menu.id ?? menu.uuid}`;
  }

  return `${menu.url}?menuId=${menu.id ?? menu.uuid}`;
};

// 1 - IFrame
// 2 - PowerBI
// 3 - Página Pronta
// 4 - Página externa
// 5 - Página Pai
