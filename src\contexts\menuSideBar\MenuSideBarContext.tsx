import { PropsWithChildren, createContext, useState } from 'react';
import { MenuSideBarContextData } from './types';

export const MenuSideBarContext = createContext<MenuSideBarContextData | null>(
  null,
);

export const MenuSideBarContextProvider = ({ children }: PropsWithChildren) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <MenuSideBarContext.Provider
      value={{ isOpen, onMenuOpenChange: setIsOpen }}
    >
      {children}
    </MenuSideBarContext.Provider>
  );
};
