import { Dialog } from '@onyma-ds/react';

import * as SC from './styles';
import { NewPageProps } from './types';
import { ClientCompaniesContextProvider } from '../../../ClientCompanies/contexts/clientCompanies';
import { useState } from 'react';
import { NewPageForm } from './Form';

export default function NewPageModal({ children }: NewPageProps) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={() => setIsOpen((state) => !state)}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay>
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">Novo menu</Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <ClientCompaniesContextProvider>
                <NewPageForm onClose={() => setIsOpen((state) => !state)} />
              </ClientCompaniesContextProvider>
            </Dialog.Body>
          </SC.Content>
        </Dialog.Overlay>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
