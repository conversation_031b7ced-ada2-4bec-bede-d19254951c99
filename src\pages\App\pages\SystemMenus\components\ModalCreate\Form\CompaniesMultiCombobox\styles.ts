import { Checkbox, Combobox, Symbol } from '@onyma-ds/react';
import { theme } from '@onyma-ds/tokens';
import styled from 'styled-components';

export const SelectedCompanies = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
`;

export const Icon = styled(Symbol).attrs({
  size: 20,
})``;

export const ComboboxTrigger = styled(Combobox.Trigger)`
  &[data-placeholder='true'] {
    & ${Icon} {
      color: ${theme.colors.black};
    }
  }
`;

export const TriggerSpan = styled.span`
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export const ComboboxItem = styled(Combobox.Item)`
  cursor: pointer;
  &[data-current='true'] {
    background-color: ${theme.colors.white};
    border-color: ${theme.colors.white};
  }

  &:hover {
    background-color: ${theme.colors.tertiary};
    border-color: ${theme.colors.tertiary};

    & label {
      color: ${theme.colors.white};
    }
  }

  &:focus-visible {
    border-color: ${theme.colors.tertiary};
  }

  &[data-selected='true'] {
    border-color: ${theme.colors.tertiary};
  }
`;

export const GroupCheckboxContainer = styled(Checkbox.Container)`
  & label {
    font-size: ${theme.fontSizes.sm};
    font-weight: 600;
    color: ${theme.colors.secondary};
    text-transform: uppercase;
  }
`;

export const CompanyCheckboxContainer = styled.div`
  display: flex;

  > div {
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
`;

export const GroupCompaniesContainer = styled.div`
  padding-left: 1.75rem;
`;
