import styled from 'styled-components';
import * as Tabs from '@radix-ui/react-tabs';

export const Container = styled.section`
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr auto;
  gap: 2rem;
`;

export const TabsRoot = styled(Tabs.Root)`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const TabsList = styled(Tabs.List)`
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const TabsTrigger = styled.button`
  color: ${({ theme }) => theme.colors.secondary};
  background-color: ${({ theme }) => theme.colors.white};
  border: 1px solid ${({ theme }) => theme.colors.secondary};
  border-radius: 4px;
  padding: 4px 8px;

  &[data-state='active'] {
    color: ${({ theme }) => theme.colors.white};
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;

export const TabsContent = styled.div`
  position: relative;
  overflow-y: auto;
  flex: 1;
`;

export const Buttons = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  button {
    width: 100%;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;

    button {
      width: auto;
    }
  }
`;
