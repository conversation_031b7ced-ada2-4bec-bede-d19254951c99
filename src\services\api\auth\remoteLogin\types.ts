import { AuthRole } from '@/@types/AuthRole';

export type Params = {
  username: string;
  password: string;
};

export type ApiResult<ResultType = null> = {
  statusCode: number;
  hasError: boolean;
  errorType: string | null;
  title: string;
  message: string;
  result: ResultType;
  microsoftAccessToken: string;
};

export type Result = ApiResult<{
  uuid: string;
  fullName: string;
  email: string;
  companies: {
    id: string;
    nome: string;
  }[];
  currentCompany?: {
    id: string;
    nome: string;
  };
  accessToken: string;
  refreshToken: string;
  currentRole: AuthRole;
  socRegister: string;
  socCode: string;
  isFirstLogin: boolean;
  roles: AuthRole[];
}>;
