import * as Tabs from '@radix-ui/react-tabs';
import { Table, UserProfileForm } from '..';
import * as SC from './styles';
import { Button } from '@onyma-ds/react';

export default function UserProfile() {
  return (
    <SC.Container>
      <SC.TabsRoot defaultValue="general-data">
        <Tabs.List asChild>
          <SC.TabsList>
            <Tabs.Trigger
              value="general-data"
              asChild
            >
              <SC.TabsTrigger>Dados gerais</SC.TabsTrigger>
            </Tabs.Trigger>
            <Tabs.Trigger
              value="menus"
              asChild
            >
              <SC.TabsTrigger>Menus</SC.TabsTrigger>
            </Tabs.Trigger>
          </SC.TabsList>
        </Tabs.List>
        <Tabs.Content
          asChild
          value="general-data"
        >
          <SC.TabsContent>
            <UserProfileForm />
          </SC.TabsContent>
        </Tabs.Content>
        <Tabs.Content
          asChild
          value="menus"
        >
          <SC.TabsContent>
            <Table />
          </SC.TabsContent>
        </Tabs.Content>
      </SC.TabsRoot>
      <SC.Buttons>
        <Button
          type="button"
          variant="secondary"
          color="white"
        >
          Salvar alterações
        </Button>
        <Button
          type="button"
          variant="secondary"
          buttonType="secondary"
        >
          Descartar alterações
        </Button>
      </SC.Buttons>
    </SC.Container>
  );
}
