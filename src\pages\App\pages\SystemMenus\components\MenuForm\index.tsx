import { Spinner } from '@/components';
import { MenuTypeId } from '@/services/api/menus/remoteLoadMenu';
import {
  Button,
  InputBox,
  InputTextBox,
  SelectBox,
  Switch,
} from '@onyma-ds/react';
import React, { useEffect, useState } from 'react';
import { CompanyCombobox, MenuCombobox } from './components';
import { menuTypeOptions } from './data';
import { useLoad } from './hooks/useLoad';
import * as SC from './styles';
import { Errors, Form, Props } from './types';
import { validateForm } from './validateForm';

const formInitial: Form = {
  name: '',
  url: '',
  company: '',
  description: '',
  image: '',
  menuType: '',
  isSubmenu: false,
  submenu: '',
  menuTab: '',
  position: 0,
  isActive: true,
  bi: '',
  biPage: '',
};

export default function MenuForm({ mode, form: formDefault, onCancel }: Props) {
  const [loading] = useState(false);
  const [form, setForm] = useState<Form>(formInitial);
  const [errors, setErrors] = useState<Errors>({});

  const {
    menuTabsOptions,
    companies,
    biReportsOptions,
    biPagesOptions,
    getMenusByTab,
  } = useLoad({ menuType: form.menuType, bi: form.bi });

  const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = event.target;
    setErrors((prev) => ({ ...prev, [name]: '' }));
    if (['isActive', 'isSubmenu'].includes(name)) {
      return setForm((prev) => ({ ...prev, [name]: checked }));
    }
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setErrors((prev) => ({ ...prev, [field]: '' }));
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const errors = validateForm(form);
    if (Object.keys(errors).length) {
      return setErrors(errors);
    }
  };

  useEffect(() => {
    setForm(formDefault || formInitial);
  }, [formDefault]);

  return (
    <SC.Container onSubmit={handleSubmit}>
      {mode === 'edit' && (
        <InputBox
          error={!!errors.isActive}
          feedbackText={errors.isActive}
        >
          <SC.SwitchContainer>
            <Switch
              id="menu-form-active"
              name="isActive"
              checked={form.isActive}
              onChange={handleFieldChange}
            />
            <Switch.Label htmlFor="menu-form-active">Ativo?</Switch.Label>
          </SC.SwitchContainer>
        </InputBox>
      )}
      <InputTextBox
        label="Nome*"
        placeholder="Digite o nome da página"
        name="name"
        value={form.name}
        onChange={handleFieldChange}
        error={!!errors.name}
        feedbackText={errors.name}
      />
      <InputTextBox
        label="URL*"
        placeholder="Digite a URL da página"
        name="url"
        value={form.url}
        onChange={handleFieldChange}
        error={!!errors.url}
        feedbackText={errors.url}
      />
      <SC.Fieldset>
        <SelectBox
          label="Aba do menu*"
          placeholder="Selecione a aba"
          options={menuTabsOptions}
          optionSelected={menuTabsOptions.find(
            (option) => option.value === form.menuTab,
          )}
          onSelect={(option) => handleSelectChange('menuTab', option.value)}
          error={!!errors.menuTab}
          feedbackText={errors.menuTab}
        />
        <InputTextBox
          type="number"
          label="Posição"
          placeholder="Digite a posição"
          name="position"
          min={0}
          value={form.position}
          onChange={handleFieldChange}
          error={!!errors.position}
          feedbackText={errors.position}
        />
      </SC.Fieldset>
      <InputBox
        error={!!errors.isSubmenu}
        feedbackText={errors.isSubmenu}
      >
        <SC.SwitchContainer>
          <Switch
            id="menu-form-isSubmenu"
            name="isSubmenu"
            checked={form.isSubmenu}
            onChange={handleFieldChange}
          />
          <Switch.Label htmlFor="menu-form-isSubmenu">É submenu?</Switch.Label>
        </SC.SwitchContainer>
      </InputBox>
      {form.isSubmenu && (
        <InputBox
          label="Menu"
          error={!!errors.submenu}
          feedbackText={errors.submenu}
        >
          <MenuCombobox
            data={getMenusByTab(form.menuTab)}
            menuSelected={form.submenu}
            onChange={(value) => handleSelectChange('submenu', value)}
          />
        </InputBox>
      )}
      <CompanyCombobox
        data={companies.sort((a, b) => a.name.localeCompare(b.name))}
        companySelected={form.company}
        onChange={(value) => handleSelectChange('company', value)}
      />
      <InputTextBox
        label="Imagem"
        placeholder="Informe a imagem da página"
        name="image"
        value={form.image}
        onChange={handleFieldChange}
        error={!!errors.image}
        feedbackText={errors.image}
      />
      <SelectBox
        label="Tipo"
        placeholder="Selecione o tipo do menu"
        options={menuTypeOptions}
        optionSelected={menuTypeOptions.find(
          (option) => option.value === form.menuType,
        )}
        onSelect={(option) => handleSelectChange('menuType', option.value)}
        error={!!errors.menuType}
        feedbackText={errors.menuType}
      />
      {form.menuType === MenuTypeId.powerBiEmbedded.toString() && (
        <>
          <SelectBox
            label="BI"
            placeholder="Selecione o BI"
            options={biReportsOptions}
            optionSelected={biReportsOptions.find(
              (option) => option.value === form.bi,
            )}
            onSelect={(option) => handleSelectChange('bi', option.value)}
            error={!!errors.bi}
            feedbackText={errors.bi}
          />
          <SelectBox
            label="BI Página"
            placeholder="Selecione a página do BI"
            options={biPagesOptions}
            optionSelected={biPagesOptions.find(
              (option) => option.value === form.biPage,
            )}
            onSelect={(option) => handleSelectChange('biPage', option.value)}
            disabled={!form.bi}
            error={!!errors.biPage}
            feedbackText={errors.biPage}
          />
        </>
      )}
      <SC.Buttons>
        <Button
          type="button"
          variant="secondary"
          buttonType="secondary"
          onClick={onCancel}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          variant="secondary"
          color="white"
        >
          {loading ? (
            <Spinner
              size={16}
              color="white"
            />
          ) : mode === 'create' ? (
            'Cadastrar'
          ) : (
            'Editar'
          )}
        </Button>
      </SC.Buttons>
    </SC.Container>
  );
}
