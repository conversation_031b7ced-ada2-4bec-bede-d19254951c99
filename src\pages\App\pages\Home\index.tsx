import { PageCard } from '@/components/layouts/cards/page-card';
import { Spinner } from '@/components/loaders/spinner';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useQuery } from '@tanstack/react-query';
import { moduleOptions } from './data';

export default function HomePage() {
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const {
    data: allMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['menus', user],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: user.companyId as string,
        perfil: user?.currentRole.id,
      }),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      {allMenus?.result?.map((menu) => {
        const moduleExist = moduleOptions.find(
          (moduleOption) =>
            moduleOption.value.toLowerCase() === menu.module.toLowerCase(),
        );
        if (moduleExist) {
          return (
            <PageCard
              key={moduleExist.name}
              title={moduleExist.name}
              icon={moduleExist.icon}
              link={moduleExist.url}
            />
          );
        }
      })}
    </div>
  );
}
