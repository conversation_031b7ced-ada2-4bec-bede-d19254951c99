import { useCallback, useEffect, useMemo, useState } from 'react';
import { useToast } from '@onyma-ds/react';
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';
import { useApi } from '@/contexts/api';
import { SystemMenu } from '../../../contexts/menus';
import { Option } from '../types';
import { MenuTypeId } from '@/services/api/menus/remoteLoadMenu';

type Params = {
  menuType: string;
  bi: string;
};

export const useLoad = ({ menuType, bi }: Params) => {
  const { addToast } = useToast();
  const { menus: menusApi, clients, bi: biApi } = useApi();

  const [menus, setMenus] = useState<SystemMenu[]>([]);
  const [companies, setClients] = useState<ClientCompany[]>([]);
  const [biReportsOptions, setBiReportsOptions] = useState<Option[]>([]);
  const [biPagesOptions, setBiPagesOptions] = useState<Option[]>([]);

  const getMenusByTab = (menuTab: string) => {
    const menu = menus.find((menu) => menu.module === menuTab);
    if (!menu) return [];
    return menu.menus;
  };

  const loadMenus = useCallback(async () => {
    menusApi
      .loadMenus()
      .then((resultMenu) => {
        setMenus(resultMenu.result);
      })
      .catch((error) => {
        addToast({
          type: 'error',
          title: error.response?.data.title || 'Carregamento dos Menus',
          description:
            error.response?.data.message || 'Erro ao carregar os menus',
          timeout: 5000,
        });
      });
  }, [menusApi, addToast]);

  const loadCompanies = useCallback(async () => {
    clients
      .loadClientCompanies()
      .then((resultClient) => {
        const allActiveCompanies = resultClient.result.filter(
          (company) => company.isActive,
        );
        setClients(allActiveCompanies);
      })
      .catch((error) => {
        addToast({
          type: 'error',
          title: error.response?.data.title || 'Carregamento das empresas',
          description:
            error.response?.data.message || 'Erro ao carregar as empresas',
          timeout: 5000,
        });
      });
  }, [clients, addToast]);

  const loadBIs = useCallback(async () => {
    biApi
      .loadBIReports()
      .then((resultBiReport) => {
        setBiReportsOptions(
          resultBiReport.result.map((report) => ({
            label: report.name,
            value: report.id.toString(),
          })),
        );
      })
      .catch((error) => {
        addToast({
          type: 'error',
          title: error.response?.data.title || 'Carregamento dos relatórios BI',
          description:
            error.response?.data.message ||
            'Erro ao carregador os relatórios BI',
          timeout: 5000,
        });
      });
  }, [biApi, addToast]);

  const loadBIPages = useCallback(async () => {
    biApi
      .loadBIReport({ id: bi })
      .then((resultBiReport) => {
        setBiPagesOptions(
          resultBiReport.result.biPage.map((report) => ({
            label: report.name,
            value: report.id.toString(),
          })),
        );
      })
      .catch((error) => {
        addToast({
          type: 'error',
          title: error.response?.data.title || 'Carregamento dos relatórios BI',
          description:
            error.response?.data.message ||
            'Erro ao carregador os relatórios BI',
          timeout: 5000,
        });
      });
  }, [biApi, bi, addToast]);

  useEffect(() => {
    Promise.all([loadMenus(), loadCompanies(), loadBIs()]);
  }, [loadMenus, loadCompanies, loadBIs]);

  useEffect(() => {
    if (menuType === MenuTypeId.powerBiEmbedded.toString()) {
      loadBIs();
    } else {
      setBiReportsOptions([]);
    }
  }, [menuType, loadBIs]);

  useEffect(() => {
    if (bi) {
      loadBIPages();
    } else {
      setBiPagesOptions([]);
    }
  }, [biApi, bi, loadBIPages]);

  const menuTabsOptions = useMemo(() => {
    return menus.map((menu) => ({ label: menu.module, value: menu.module }));
  }, [menus]);

  return {
    menuTabsOptions,
    companies,
    biReportsOptions,
    biPagesOptions,
    getMenusByTab,
  };
};
