import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  button {
    display: grid;
    place-items: center;
  }
`;

export const BadgeWarning = styled.section`
  background-color: ${({ theme }) => theme.colors.warning_extra_light};
  color: ${({ theme }) => theme.colors.warning_extra_dark};
  padding: 1rem;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const Fields = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;
