import { Pagination } from '@/@types/Pagination';
import { SystemMenu as SystemMenuApi } from '@/services/api/menus/remoteLoadMenus';

export type MenusContextData = {
  loading: { listing: boolean };
  data: SystemMenu[];
  filters: Filters;
  pagination: Pagination;
  refetch: () => Promise<void>;
  onFiltersChange: React.Dispatch<React.SetStateAction<Filters>>;
  onPaginationChange: React.Dispatch<React.SetStateAction<Pagination>>;
};

export type SystemMenu = SystemMenuApi;

export type Menu = SystemMenuApi['menus'][0];

export type Filters = {
  name?: string;
  url?: string;
  isActive?: string;
  profile?: string;
  submenu?: string;
};
