import {
  Button,
  InputDateBox,
  InputTextBox,
  SelectBox,
  Switch,
} from '@onyma-ds/react';
import * as SC from './styles';
import { Props } from './types';
import { OperatorCombobox } from './components';

export default function OperatorForm({ mode, onCancel }: Props) {
  return (
    <SC.Container>
      <SC.FormSection>
        <InputTextBox
          label="Nome"
          defaultValue="[MVP] Dia"
          readOnly
          disabled
        />
        <OperatorCombobox />
        <SelectBox
          label="Selecione o tipo de produto"
          placeholder="Selecione o tipo"
          options={[
            { label: 'Saúde', value: 'saude' },
            { label: 'Odonto', value: 'odonto' },
            { label: 'Saúde + Odonto', value: 'saudeeodonto' },
          ]}
        />
        <InputTextBox
          label="Dia de vencimento da fatura"
          placeholder="Informe o dia"
        />
        <InputTextBox
          label="Breakeven"
          placeholder="0,00%"
        />
        <SC.FieldsBox>
          <SC.SwitchContainer>
            <Switch />
            <Switch.Label>Coparticipação</Switch.Label>
          </SC.SwitchContainer>
          <SC.SwitchContainer>
            <Switch />
            <Switch.Label>Reembolso</Switch.Label>
          </SC.SwitchContainer>
        </SC.FieldsBox>
      </SC.FormSection>
      <SC.FieldsetReadjustment>
        <legend>Reajuste</legend>
        <SC.FormSection>
          <InputTextBox
            label="Breakeven"
            placeholder="0,00%"
          />
          <InputTextBox
            label="Aplicado"
            placeholder="0,00%"
          />
          <InputDateBox label="Próximo reajuste" />
        </SC.FormSection>
      </SC.FieldsetReadjustment>
      <SC.FieldsetReadjustment>
        <legend>Simulação de reajuste</legend>
        <SC.FormSection>
          <InputTextBox
            label="Vigência atual"
            placeholder="0,00%"
          />
          <InputTextBox
            label="Últimos 12 meses"
            placeholder="0,00%"
          />
        </SC.FormSection>
      </SC.FieldsetReadjustment>
      <SC.Buttons>
        <Button
          type="button"
          variant="secondary"
          buttonType="secondary"
          onClick={onCancel}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          variant="secondary"
          color="white"
        >
          {mode === 'create' ? 'Cadastrar' : 'Editar'}
        </Button>
      </SC.Buttons>
    </SC.Container>
  );
}
