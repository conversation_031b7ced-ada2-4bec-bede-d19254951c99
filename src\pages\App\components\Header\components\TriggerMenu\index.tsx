import { useMenuSideBar } from '@/contexts/menuSideBar';
import { Icons } from '@/components';
import * as SC from './styles';

export default function TriggerMenu() {
  const { onMenuOpenChange } = useMenuSideBar();

  const handleMenuOpenChange = () => {
    onMenuOpenChange((prevState) => !prevState);
  };

  return (
    <SC.Container
      type="button"
      onClick={handleMenuOpenChange}
    >
      <Icons.Symbol
        name="menu"
        size={24}
      />
    </SC.Container>
  );
}
