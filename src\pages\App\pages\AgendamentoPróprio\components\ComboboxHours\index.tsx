import { Combobox, Text } from '@onyma-ds/react';
import { useState } from 'react';
import { Props } from './types';

export default function ComboboxHours({ data, value, onChange }: Props) {
  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    setOpen(false);
    onChange(value);
  };

  const optionSelectedFound = value
    ? data.find((option) => option.value === value)
    : null;

  return (
    <Combobox.Root
      open={open}
      onOpenChange={setOpen}
    >
      <Combobox.Trigger
        placeholder=""
        data-placeholder={!optionSelectedFound}
      >
        {optionSelectedFound ? optionSelectedFound.label : 'Selecionar menu'}
      </Combobox.Trigger>
      <Combobox.Portal>
        <Combobox.Content placeholder="">
          <Combobox.Command>
            <Combobox.Input
              crossOrigin
              placeholder="Buscar..."
            />
            <Combobox.List>
              <Combobox.Empty>
                <Text>Nenhum resultado encontrado</Text>
              </Combobox.Empty>
              <Combobox.Viewport>
                <Combobox.Group>
                  {data.map((option) => (
                    <Combobox.Item
                      key={option.value}
                      placeholder=""
                      value={option.value}
                      onSelect={() => handleSelect(option.value)}
                    >
                      {option.label}
                    </Combobox.Item>
                  ))}
                </Combobox.Group>
              </Combobox.Viewport>
            </Combobox.List>
          </Combobox.Command>
        </Combobox.Content>
      </Combobox.Portal>
    </Combobox.Root>
  );
}
