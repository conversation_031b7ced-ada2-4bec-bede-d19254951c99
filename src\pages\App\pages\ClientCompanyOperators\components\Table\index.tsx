import { Table as TableDS } from '@onyma-ds/react';
import { Icons } from '@/components';
import { BadgeStatus, ButtonSort, ModalOperator } from '..';
import * as SC from './styles';

export default function Table() {
  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th title="Operadora">
              <ButtonSort sort="desc">Operadora</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Produto">
              <ButtonSort>Produto</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Breakeven">
              <ButtonSort>Breakeven</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Coparticipação">
              <ButtonSort>Coparticipação</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Reembolso">
              <ButtonSort>Reembolso</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Vencimento da fatura">
              <ButtonSort>Vencimento da fatura</ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Próx. Reajuste">
              <ButtonSort>Próx. Reajuste</ButtonSort>
            </TableDS.Th>
            <TableDS.Th>
              <ModalOperator mode="create">
                <SC.Trigger>
                  <Icons.Symbol
                    name="add"
                    size={16}
                  />{' '}
                  operadora
                </SC.Trigger>
              </ModalOperator>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item) => (
            <TableDS.Tr key={item}>
              <TableDS.Td>Operadora {item + 1}</TableDS.Td>
              <TableDS.Td>Saúde</TableDS.Td>
              <TableDS.Td>70%</TableDS.Td>
              <TableDS.Td>
                <BadgeStatus variant="success">Sim</BadgeStatus>
              </TableDS.Td>
              <TableDS.Td>
                <BadgeStatus variant="error">Não</BadgeStatus>
              </TableDS.Td>
              <TableDS.Td>Dia 1</TableDS.Td>
              <TableDS.Td>01/09/2021</TableDS.Td>
              <TableDS.Td>
                <div>
                  <ModalOperator mode="edit">
                    <SC.Trigger>
                      <Icons.Symbol
                        name="edit_square"
                        size={16}
                      />
                    </SC.Trigger>
                  </ModalOperator>
                  <ModalOperator mode="edit">
                    <SC.Trigger>
                      <Icons.RABs.BsThreeDots size={16} />
                    </SC.Trigger>
                  </ModalOperator>
                </div>
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
