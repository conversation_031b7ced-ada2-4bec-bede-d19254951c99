import { z } from 'zod';

const requiredMessage = 'Esse campo é obrigatório.';
const objectField = z.object(
  {
    label: z.string(),
    value: z.string(),
  },
  { message: requiredMessage },
);

export const EditMenuSchema = z
  .object({
    ativo: z.coerce.boolean().optional(),
    nome: z.string({ required_error: requiredMessage }),
    descricao: z.coerce.string().optional(),
    url: z.string().optional(),
    pagina: objectField.optional(),
    icone: z.coerce.string().optional(),
    agenda: z
      .object(
        {
          label: z.string(),
          value: z.string(),
          compromissos: z.array(z.string()),
        },
        { message: requiredMessage },
      )
      .optional(),
    compromisso: objectField.optional(),
    relatorio: objectField.optional(),
    biRelatorio: objectField.optional(),
    modulo: objectField,
    localizacao: objectField.optional(),
    submenu: z.coerce.boolean().optional(),
    ordem: z.coerce
      .number({ message: requiredMessage })
      .int()
      .positive({ message: 'Deve ser maior que 0.' }),
    menuTipo: objectField,
    companies: z.string().optional(),
    userGroup: z.string().min(1, { message: requiredMessage }),
  })
  .superRefine((data, ctx) => {
    if (data.menuTipo.value === '1' && !data.url) {
      ctx.addIssue({
        path: ['url'],
        code: 'custom',
        message: requiredMessage,
      });
    }

    if (data.submenu) {
      if (data.localizacao?.value === '' || !data.localizacao) {
        ctx.addIssue({
          path: ['localizacao'],
          code: 'custom',
          message: requiredMessage,
        });
      }
    }

    if (data.menuTipo.value === '2') {
      if (!data.relatorio) {
        ctx.addIssue({
          path: ['relatorio'],
          code: 'custom',
          message: requiredMessage,
        });
      }
      if (!data.biRelatorio || data.biRelatorio.value === '') {
        ctx.addIssue({
          path: ['biRelatorio'],
          code: 'custom',
          message: requiredMessage,
        });
      }
    }

    if (data.menuTipo.value === '3') {
      if (!data.pagina) {
        ctx.addIssue({
          path: ['pagina'],
          code: 'custom',
          message: requiredMessage,
        });
      }
    }
  });

export type EditMenuType = z.infer<typeof EditMenuSchema>;
