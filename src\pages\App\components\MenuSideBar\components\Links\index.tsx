import { Icons } from '@/components';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { Text } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { MenuLink } from '..';
import { moduleOptions } from './data';
import * as SC from './styles';

export default function Links() {
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const { data: allMenus } = useQuery({
    queryKey: ['menus', user],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: user.companyId as string,
        perfil: user?.currentRole.id,
      }),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  return (
    <SC.Container>
      <SC.List>
        <MenuLink
          href="/app/home"
          title="Navegar para a Home"
        >
          <Icons.RAIo5.IoHomeOutline size={20} />
          <Text type="body_03">Início</Text>
        </MenuLink>
        {allMenus?.result?.map((menu) => {
          const moduleExist = moduleOptions.find(
            (moduleOption) => moduleOption.value === menu.module,
          );
          if (moduleExist) {
            return (
              <MenuLink
                key={moduleExist.name}
                href={String(moduleExist.url)}
                title={`Navegar para ${moduleExist.name}`}
              >
                {moduleExist.icon}
                <Text type="body_03">{moduleExist.name}</Text>
              </MenuLink>
            );
          }
        })}
      </SC.List>
    </SC.Container>
  );
}
