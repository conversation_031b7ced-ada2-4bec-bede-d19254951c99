import styled from 'styled-components';

export const Container = styled.main`
  height: 100%;
  padding: 1rem 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    padding: 1.5rem;
  }
`;

export const Cards = styled.ul`
  width: 100%;
  padding: 1rem 0.75rem;
  display: grid;
  grid-template-columns: 1fr;
  justify-content: center;
  gap: 1.5rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    grid-template-columns: 1fr 1fr 1fr;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xxl}px`}) {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  }
`;

export const CardItem = styled.li`
  list-style-type: none;
  width: 100%;
  height: 100%;
`;
