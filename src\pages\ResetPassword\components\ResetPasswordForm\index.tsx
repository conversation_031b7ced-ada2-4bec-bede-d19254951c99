import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@onyma-ds/react';
import { ChevronLeft } from 'lucide-react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ResetPasswordFormProps } from './types';
import { ResetPasswordFormData, resetPasswordFormSchema } from './validation';
import { Link } from 'react-router-dom';

export default function ResetPasswordForm({
  onLinkSent,
}: ResetPasswordFormProps) {
  const { auth } = useApi();
  const { addToast } = useToast();
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordFormSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit: SubmitHandler<ResetPasswordFormData> = async ({ email }) => {
    try {
      await auth.sendResetPasswordLink({ email });
      onLinkSent(email);
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Erro ao tentar recuperar senha',
        description: error.message,
        timeout: 5000,
      });
    }
  };

  return (
    <form
      className="flex flex-col gap-10 w-[360px]"
      onSubmit={handleSubmit(onSubmit)}
    >
      <h1 className="text-center text-2xl font-semibold">Recuperar senha</h1>

      <div>
        <Input
          id="email"
          type="email"
          label="E-mail"
          placeholder="Digite o seu e-mail"
          errorMessage={errors.email?.message}
          {...register('email')}
        />
      </div>

      <div className="flex flex-col items-center gap-4">
        <Button
          type="submit"
          disabled={!!errors.email || isSubmitting}
          isLoading={isSubmitting}
          className="w-full"
        >
          Recuperar senha
        </Button>
        <Link to="/login">
          <Button
            className="text-primary hover:text-primary/95 w-full"
            variant="ghost"
            type="button"
            startIcon={<ChevronLeft />}
          >
            Voltar ao login
          </Button>
        </Link>
      </div>
    </form>
  );
}
