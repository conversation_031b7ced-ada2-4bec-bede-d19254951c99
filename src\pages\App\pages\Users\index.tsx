import { Icons } from '@/components';
import { PageHeader } from '../../components';
import { Users } from './components';
import { UsersContextProvider } from './contexts/users';
import * as SC from './styles';

export default function UsersPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Usuários</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <UsersContextProvider>
        <Users />
      </UsersContextProvider>
    </SC.Container>
  );
}
