export const email = (value: string): boolean => {
  if (!value) return false;
  if (!value.includes('@')) return false;
  const [user, domain] = value.split('@');
  if (!user || !domain) return false;
  if (user.replace(/[^a-zA-Z0-9]/gi, '').length === 0) return false;
  if (domain.replace(/[^a-zA-Z0-9]/gi, '').length === 0) return false;
  const [domainFirstPart, domainSecondPart] = domain.split('.');
  if (!domainFirstPart || !domainSecondPart) return false;
  return true;
};
