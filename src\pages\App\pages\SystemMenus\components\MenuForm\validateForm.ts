import { Errors, Form } from './types';

export const validateForm = (form: Form) => {
  const errors: Errors = {};

  if (!form.name) {
    errors.name = 'Campo obrigatório';
  }

  if (!form.url) {
    errors.url = 'Campo obrigatório';
  }

  if (!form.menuTab) {
    errors.menuTab = 'Campo obrigatório';
  }

  if (form.position && form.position < 0) {
    errors.position = 'Deve ser maior ou igual a 0';
  }

  if (form.isSubmenu && !form.submenu) {
    errors.submenu = 'Campo obrigatório';
  }

  if (form.menuType === 'powerBiEmbedded') {
    if (!form.bi) {
      errors.bi = 'Campo obrigatório';
    }
    if (!form.biPage) {
      errors.biPage = 'Campo obrigatório';
    }
  }

  return errors;
};
