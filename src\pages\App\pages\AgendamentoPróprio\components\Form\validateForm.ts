import { validations } from '@/utils/validations';
import { Errors, Form } from './types';

export const validateForm = (form: Form) => {
  const errors: Errors = {};

  if (!form.company) {
    errors.company = 'Campo obrigatório';
  }

  if (!form.registration) {
    errors.registration = 'Campo obrigatório';
  }

  if (!form.name) {
    errors.name = 'Campo obrigatório';
  }

  if (!validations.email(form.email)) {
    errors.email = 'Campo obrigatório';
  }

  if (!form.address) {
    errors.address = 'Campo obrigatório';
  }

  if (!form.date1) {
    errors.date1 = 'Campo obrigatório';
  }

  if (!form.date2) {
    errors.date2 = 'Campo obrigatório';
  }

  if (!form.date3) {
    errors.date3 = 'Campo obrigatório';
  }

  if (!form.hour1) {
    errors.hour1 = 'Campo obrigatório';
  }

  if (!form.hour2) {
    errors.hour2 = 'Campo obrigatório';
  }

  if (!form.hour3) {
    errors.hour3 = 'Campo obrigatório';
  }

  return {
    isValid: Object.values(errors).length === 0,
    errors,
  };
};
