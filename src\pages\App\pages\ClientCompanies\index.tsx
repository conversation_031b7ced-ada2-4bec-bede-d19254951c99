import { Icons } from '@/components';
import { PageHeader } from '../../components';
import * as SC from './styles';
import { Companies } from './components';
import { ClientCompaniesContextProvider } from './contexts/clientCompanies';

export default function ClientCompaniesPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Empresas clientes</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <ClientCompaniesContextProvider>
        <Companies />
      </ClientCompaniesContextProvider>
    </SC.Container>
  );
}
