import { EmptyPageMenu } from '@/components/layouts/cards/empty';
import { PageCard } from '@/components/layouts/cards/page-card';
import { Spinner } from '@/components/loaders/spinner';
import { useApi } from '@/contexts/api';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { handleCardClick } from '../Apps/utils';

export default function SubmenuPage() {
  const [searchParams] = useSearchParams();
  const submenuId = searchParams.get('id');

  const {
    menus: { loadMenu },
  } = useApi();

  const {
    data: allSubMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['submenu', submenuId],
    queryFn: () =>
      loadMenu({
        id: String(submenuId),
      }),
    refetchOnWindowFocus: false,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      {allSubMenus?.result.menus ? (
        allSubMenus?.result.menus[0].menus?.map((menu) => (
          <PageCard
            key={menu.title}
            title={menu.title}
            icon={menu?.icon}
            link={handleCardClick(menu)}
            targetBlank={Number(menu.type) === 4}
          />
        ))
      ) : (
        <EmptyPageMenu />
      )}
    </div>
  );
}
