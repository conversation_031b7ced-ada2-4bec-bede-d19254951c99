import { BreadcrumbMenu } from '@/components/layouts/breadcrumb/breadcrumb';
import { DocumentTitleManager } from '@/components/layouts/DocumentTitleManager';
import { Header } from '@/components/layouts/header';
import { SidebarComp } from '@/components/layouts/sidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { useAuth } from '@/contexts/auth';
import { Navigate, Outlet, useMatch } from 'react-router-dom';

export default function PrivateLayout() {
  const { user } = useAuth();

  const matchAppRoute = useMatch({ path: '/app', end: true });

  if (!user) {
    return (
      <Navigate
        to="/logout"
        replace
      />
    );
  }

  if (matchAppRoute) {
    return (
      <Navigate
        to="/app/home"
        replace
      />
    );
  }

  return (
    <SidebarProvider>
      <DocumentTitleManager />
      <div className="min-h-screen flex flex-col flex-1">
        <Header />
        <div className="flex w-full h-full">
          <SidebarComp />

          <div className="w-full h-full bg-[#27B5BF0A]  ">
            <BreadcrumbMenu />
            <div className="max-h-[calc(100vh-144px)] h-full overflow-y-auto bg-[#27B5BF0A]">
              <Outlet />
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
