/* eslint-disable react-hooks/exhaustive-deps */
import { Spinner } from '@/components';
import { InputText, Pagination } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import Table from '../Table';

import { Pagination as PaginationType } from '@/@types/Pagination';
import { useApi } from '@/contexts/api';
import { Calendar } from '@/services/api/calendar/remoteLoadCalendars';
import NewAgendaModal from '../CreateAgenda';
import DeleteAgenda from '../DeleteAgenda';
import { CalendarType } from '../types';
import * as SC from './styles';
import EditAgendaModal from '../EditAgenda';

const applyPagination = (data: Calendar[], pagination: PaginationType) => {
  const start = (pagination.page - 1) * pagination.perPage;
  const end = start + pagination.perPage;
  return data.slice(start, end);
};

export default function CalendarManagement() {
  const {
    calendar: { loadCalendars },
  } = useApi();
  const [agendaToEdit, setAgendaToEdit] = useState<Calendar>({} as Calendar);
  const [loading, setLoading] = useState(false);
  const [allCalendars, setAllCalendars] = useState<Calendar[]>([]);
  const [search, setSearch] = useState('');
  const [modals, setModals] = useState({
    newCalendar: false,
    editCalendar: false,
    deleteCalendar: false,
  });
  const [pagination, setPagination] = useState<PaginationType>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const { data } = useQuery({
    queryKey: ['allCalendars'],
    queryFn: () => loadCalendars(),
  });

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
      perPage: itemsPerPage,
    }));
  };

  useEffect(() => {
    setLoading(true);
    setAllCalendars(data?.result ?? []);
    setLoading(false);
    setPagination((prev) => ({
      ...prev,
      totalItems: data?.result.length ?? 0,
    }));
    return;
  }, [data]);

  useEffect(() => {
    const filteredCalendars = data?.result.filter((calendar) => {
      return calendar.nome.toLowerCase().includes(search.toLowerCase());
    });
    if (!filteredCalendars) return;
    setAllCalendars(filteredCalendars);
  }, [search]);

  useEffect(() => {
    if (!data) return;

    setAllCalendars(() => applyPagination(data?.result, pagination));
  }, [pagination, data]);

  if (loading) {
    return (
      <SC.ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerLoading>
    );
  }

  const handleOpenCreateModal = () => {
    setModals((prev) => {
      return {
        ...prev,
        newCalendar: !prev.newCalendar,
      };
    });
  };

  const handleSelectAgenda = (selectedAgenda: CalendarType) => {
    setAgendaToEdit(selectedAgenda);
  };

  const handleOpenEditModal = () => {
    setModals((prev) => {
      return {
        ...prev,
        editCalendar: !prev.editCalendar,
      };
    });
  };

  const setDeleteAgendaState = (selectedAgenda: CalendarType) => {
    setAgendaToEdit(selectedAgenda);
    setModals((prev) => {
      return {
        ...prev,
        deleteCalendar: !prev.deleteCalendar,
      };
    });
  };

  return (
    <>
      <DeleteAgenda
        agendaId={agendaToEdit.id}
        isOpen={modals.deleteCalendar}
        setModalState={() =>
          setModals((prev) => {
            return {
              ...prev,
              deleteCalendar: !prev.deleteCalendar,
            };
          })
        }
      />
      <NewAgendaModal
        isOpen={modals.newCalendar}
        setModalState={handleOpenCreateModal}
      />
      <EditAgendaModal
        isOpen={modals.editCalendar}
        setModalState={handleOpenEditModal}
        selectedAgenda={agendaToEdit}
      />
      <SC.Container>
        <SC.QuickFilterBox>
          <InputText
            placeholder="Pesquisar..."
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
        </SC.QuickFilterBox>
        <SC.TableBox>
          <Table
            agendas={allCalendars}
            onOpenAgendaCreation={handleOpenCreateModal}
            onDeleteAgenda={setDeleteAgendaState}
            onEditAgenda={handleOpenEditModal}
            handleSelectAgenda={handleSelectAgenda}
          />
        </SC.TableBox>
        <SC.PaginationBox>
          <Pagination
            currentPage={pagination.page}
            itemsPerPage={pagination.perPage}
            totalItems={pagination.totalItems}
            itemsPerPageOptions={[10, 20, 30, 50, 100]}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </SC.PaginationBox>
      </SC.Container>
    </>
  );
}
