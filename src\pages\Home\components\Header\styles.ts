import { colorHexToRgba } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.header`
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: ${({ theme }) =>
    colorHexToRgba(theme.colors.quaternary_extra_light, 0.1)};
  backdrop-filter: blur(4px);
  margin: 0 auto;
  padding: 1rem 2rem 1rem 1rem;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 0.25rem;

  #c-menu-navigation {
    grid-column: span 2;
  }

  &[data-menu-state='open'] {
    background-color: ${({ theme }) =>
      colorHexToRgba(theme.colors.quaternary_extra_light, 0.4)};
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    background-color: ${({ theme }) => theme.colors.white};

    #c-menu-navigation {
      grid-column: span 1;
    }

    &[data-menu-state='open'] {
      background-color: inherit;
    }
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    max-width: 720px;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.lg}px`}) {
    max-width: 960px;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    max-width: 1144px;
  }
`;

export const MenuTrigger = styled.button`
  background-color: inherit;
  color: ${({ theme }) => theme.colors.primary_extra_dark};
  height: min-content;
  border: 1px solid
    ${({ theme }) => colorHexToRgba(theme.colors.primary_extra_dark, 0.1)};
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  transition: filter 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    filter: brightness(0.9);
  }
`;
