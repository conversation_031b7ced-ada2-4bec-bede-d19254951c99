import { validations } from '@/utils/validations';
import { Errors, Form } from './types';

export const validateForm = (form: Form, mode: 'create' | 'edit') => {
  const errors: Errors = {};

  for (const [key, value] of Object.entries(form)) {
    switch (key as keyof Form) {
      case 'name': {
        if (!value) {
          errors.name = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'email': {
        if (!value) {
          errors.email = 'Campo obrigatório';
          break;
        }
        if (!validations.email(value as string)) {
          errors.email = 'Campo inválido';
          break;
        }
        break;
      }
      case 'password': {
        if (mode === 'create' && !value) {
          errors.password = 'Campo obrigatório';
          break;
        }
        if (mode === 'edit' && form.changePassword && !value) {
          errors.password = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'profiles': {
        if ((value as string[]).length === 0) {
          errors.profiles = 'Selecione pelo menos um perfil';
          break;
        }
      }
    }
  }

  return {
    isValid: Object.values(errors).every((error) => !error),
    errors,
  };
};
