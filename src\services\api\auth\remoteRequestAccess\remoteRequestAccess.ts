import axios from 'axios';
import { RemoteRequestAccess } from './types';

export const remoteRequestAccess: RemoteRequestAccess = async (params) => {
  const token = import.meta.env.VITE_REQUEST_ACCESS_TOKEN;

  const response = await axios.post(
    `${import.meta.env.VITE_REQUEST_ACCESS_URL}`,
    null,
    {
      params: {
        token: token,
        it00049I000001: params.name,
        it00049I000002: params.company,
        it00049I000003: params.email,
        it00049I000004: params.role,
      },
    },
  );

  return response;
};
