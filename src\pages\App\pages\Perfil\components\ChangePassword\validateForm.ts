import { Form } from './types';

export const validateForm = (form: Form) => {
  const errors: Partial<Form> = {};

  if (!form.currentPassword) {
    errors.currentPassword = 'Senha atual é obrigatória';
  }

  if (!form.password) {
    errors.password = 'Nova senha é obrigatória';
  }

  if (!form.passwordRepeat) {
    errors.passwordRepeat = 'Confirmação de senha é obrigatória';
  }

  if (form.password !== form.passwordRepeat) {
    errors.passwordRepeat = 'As senhas não coincidem';
  }

  return {
    isValid: Object.values(errors).length === 0,
    errors: errors,
  };
};
