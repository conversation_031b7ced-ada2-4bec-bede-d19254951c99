import { Button } from '@onyma-ds/react';

import * as SC from './styles';
import { DeleteGroupModalState } from '../types';

export default function DeleteUserLayout({
  onClose,
  setHasError,
}: DeleteGroupModalState) {
  const handleDelete = () => {
    setHasError(true);
  };
  return (
    <SC.Container>
      <span>
        Tem certeza que deseja excluir o grupo de usuários? Esta ação não poderá
        ser desfeita.
      </span>
      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="danger"
          type="submit"
          color="white"
          onClick={handleDelete}
        >
          Sim, Excluir
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
