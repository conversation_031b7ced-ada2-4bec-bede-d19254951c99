/* eslint-disable react-hooks/exhaustive-deps */
import { Spinner } from '@/components';
import { InputText, Pagination } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import EditGroupModal from '../EditGroupModal';
import NewGroupModal from '../NewGroupModal';
import Table from '../Table';
import { UserGroupType } from '../types';

import { Pagination as PaginationType } from '@/@types/Pagination';
import { useApi } from '@/contexts/api';
import * as SC from './styles';

const applyPagination = (data: UserGroupType[], pagination: PaginationType) => {
  const start = (pagination.page - 1) * pagination.perPage;
  const end = start + pagination.perPage;
  return data.slice(start, end);
};

export default function AgendaPage() {
  const {
    userGroup: { loadUserGroups },
  } = useApi();
  const [currentUserGroupToEdit, setCurrentUserGroupToEdit] =
    useState<UserGroupType>({} as UserGroupType);
  const [loading, setLoading] = useState(false);
  const [allUserGroups, setAllUserGroups] = useState<UserGroupType[]>([]);
  const [search, setSearch] = useState('');
  const [modals, setModals] = useState({
    newGroup: false,
    editGroup: false,
    deleteGroup: false,
  });
  const [pagination, setPagination] = useState<PaginationType>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const { data } = useQuery({
    queryKey: ['userGroups'],
    queryFn: () => loadUserGroups(),
  });

  const setNewGroupModalState = () => {
    setLoading(false);
    setModals((prev) => {
      return {
        ...prev,
        newGroup: !prev.newGroup,
      };
    });
  };

  const setDeleteGroupModalState = () => {
    setLoading(false);
    setModals((prev) => {
      return {
        ...prev,
        deleteGroup: !prev.deleteGroup,
      };
    });
  };

  const setEditGroupModalState = () => {
    setLoading(false);
    setModals((prev) => {
      return {
        ...prev,
        editGroup: !prev.editGroup,
      };
    });
  };

  const handleSelectUserGroupToEdit = (userGroup: UserGroupType) => {
    setCurrentUserGroupToEdit(userGroup);
  };

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
      perPage: itemsPerPage,
    }));
  };

  useEffect(() => {
    setAllUserGroups(data?.result || []);
    setPagination((prev) => ({
      ...prev,
      totalItems: data?.result.length ?? 0,
    }));
    return;
  }, [data]);

  useEffect(() => {
    const filteredUserGroups = data?.result.filter((userGroup) => {
      return userGroup.nome.toLowerCase().includes(search.toLowerCase());
    });
    if (!filteredUserGroups) return;
    setAllUserGroups(filteredUserGroups);
  }, [search]);

  useEffect(() => {
    if (!data) return;

    setAllUserGroups(() => applyPagination(data?.result, pagination));
  }, [pagination, data]);

  if (loading) {
    return (
      <SC.ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerLoading>
    );
  }
  return (
    <>
      {/* <DeleteUserGroupModal
        isOpen={modals.deleteGroup}
        setModalState={setDeleteGroupModalState}
        groupId="14"
      /> */}
      <EditGroupModal
        isOpen={modals.editGroup}
        setModalState={setEditGroupModalState}
        defaultValues={currentUserGroupToEdit}
      />
      <NewGroupModal
        isOpen={modals.newGroup}
        setModalState={setNewGroupModalState}
      />
      <SC.Container>
        <SC.QuickFilterBox>
          <InputText
            placeholder="Pesquisar..."
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
        </SC.QuickFilterBox>
        <SC.TableBox>
          <Table
            handleSelectUserGroupToEdit={handleSelectUserGroupToEdit}
            setNewGroupModalState={setNewGroupModalState}
            setEditGroupModalState={setEditGroupModalState}
            handleDeleteUserGroup={setDeleteGroupModalState}
            userGroups={allUserGroups}
          />
        </SC.TableBox>
        <SC.PaginationBox>
          <Pagination
            currentPage={pagination.page}
            itemsPerPage={pagination.perPage}
            totalItems={pagination.totalItems}
            itemsPerPageOptions={[10, 20, 30, 50, 100]}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </SC.PaginationBox>
      </SC.Container>
    </>
  );
}
