import { Dialog } from '@onyma-ds/react';

import * as SC from './styles';
import { NewGroupModalProps } from './types';
import NewUserGroupForm from './Form';

export default function NewGroupModal({
  isOpen,
  setModalState,
}: NewGroupModalProps) {
  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">Novo grupo de usuários</Dialog.Title>
          </Dialog.Header>
          <Dialog.Body>
            <NewUserGroupForm onClose={setModalState} />
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
