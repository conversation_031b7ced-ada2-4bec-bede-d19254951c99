import { useAuth } from '@/contexts/auth';
import { useLoadOptions } from '../../Users/<USER>/UserForm/hooks/useLoadOptions';
import { useFilters } from '../Contexts/useFilters';

import SelectCompany from '@/pages/App/components/Header/components/ProfileChange/CompanyCheckCombobox';
import { ComboboxFilter } from './ComboBox';
import { DateFilter } from './DateFilter';
import { Container } from './styles';
import { LabelAndValueAndLogo } from '@/@types/LabelAndValue';

export function Filters() {
  const { profilesOptions, companiesOptions } = useLoadOptions();
  const { user } = useAuth();
  const {
    companiesFilter,
    setCompaniesFilter,
    profileFilter,
    setProfileFilter,
    endDateFilter,
    setEndDateFilter,
    startDateFilter,
    setStartDateFilter,
  } = useFilters();

  const isUserAdmin = user?.currentRole.name.includes('Administrador');

  return (
    <Container>
      {isUserAdmin && (
        <SelectCompany
          isFilter
          placeholder="Empresa"
          company={companiesOptions.map((company) => {
            return {
              label: company.name,
              value: company.id,
              logo: company.logo,
            };
          })}
          selectedCompany={companiesFilter}
          setSelectedCompany={(value) =>
            setCompaniesFilter(value as LabelAndValueAndLogo)
          }
        />
      )}
      {isUserAdmin && (
        <ComboboxFilter
          label="Perfil"
          options={profilesOptions.map((role) => ({
            label: role.nome,
            value: role.id,
          }))}
          selectedItems={profileFilter}
          setSelectedItems={(value) => setProfileFilter(value)}
        />
      )}
      {/* <ComboboxFilter
        label="Tipo do arquivo"
        options={options}
        selectedItems={fileTypeFilter}
        setSelectedItems={(value) => setFileTypeFilter(value)}
      /> */}
      <DateFilter
        label="Data"
        setEndDateSelected={setEndDateFilter}
        setStartDateSelected={setStartDateFilter}
        startDateSelected={startDateFilter}
        endDateSelected={endDateFilter}
      />
    </Container>
  );
}
