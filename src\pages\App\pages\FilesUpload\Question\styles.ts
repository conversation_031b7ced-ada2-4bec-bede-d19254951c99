import styled from 'styled-components';

export const Container = styled.section`
  height: 100%;
  width: 100%;

  display: flex;
  flex-direction: column;
  align-items: center;

  gap: 0.875rem;

  .question {
    width: 100%;

    display: flex;
    align-items: center;

    gap: 0.5rem;
    white-space: nowrap;
    span {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      height: 24px;
      width: 24px;
      background-color: ${({ theme }) => theme.colors.primary};
      color: ${({ theme }) => theme.colors.white};
      border-radius: 1rem;
    }
    p {
      font-size: 1rem;
      font-weight: 500;
    }
  }
`;
