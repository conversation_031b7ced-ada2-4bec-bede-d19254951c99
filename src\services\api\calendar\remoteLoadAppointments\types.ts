import { ApiResult } from '@/@types/ApiResult';

type Params = {
  empresa?: string;
  codigo?: string;
  chave?: string;
  tipoSaida?: string;
  dataInicial: Date;
  dataFinal: Date;
  codigoUsuarioAgenda: string;
  codigoEmpresaBusca?: string;
  matriculaFuncionarioBusca?: string;
};

export type Appointment = {
  codigoUsuarioAgenda: string;
  nomeAgenda: string;
  codigoEmpresa: string;
  nomeEmpresa: string;
  codigoFuncionario: string;
  nomeFuncionario: string;
  cpfFuncionario: string;
  dataNascimentoFuncionario: string;
  sexoFuncionario: string;
  dataCompromisso: string;
  tipoCompromisso: string;
  rgFuncionario: string;
  ufRgFuncionario: string;
  celularFuncionario: string;
  emailFuncionario: string;
  logradouroFuncionario: string;
  numeroLogradouroFuncionario: string;
  complementoLogradouroFuncionario: string;
  bairroFuncionario: string;
  cidadeFuncionario: string;
  ufFuncionario: string;
  cepFuncionario: string;
  horaInicio: string;
  horaFim: string;
  nomeCompromisso: string;
  nomeTipoCompromisso: string;
  nomeProfissionalAgenda: string;
  horaChegada: string;
  horaSaida: string;
  situacao: string;
  detalhes: string;
  atendimentoPrioritario: string;
  codigoUsuarioInclusao: string;
  dataInclusao: string;
  horaInclusao: string;
  codigoSequencialFicha: string;
  statusFuncionario: string;
  compromissoRemarcado: string;
  dataCompromissoRemarcado: string;
  situacaoCompromissoRemarcado: string;
  tipoCompromissoRemarcado: string;
  nomeCompromissoRemarcado: string;
  unidadeFuncionario: string;
  setorFuncionario: string;
  cargoFuncionario: string;
  enviarEmail: string;
  dataEmail: string;
  horaEmail: string;
  email: string;
  statusEmail: string;
  videoChamada: string;
  motivoCancelamento: string;
  codigoAgendamento: string;
  codigoAgendamentoRemarcado: string;
};

type Result = ApiResult<Appointment[]>;

export type RemoteLoadAppointments = (params: Params) => Promise<Result>;
