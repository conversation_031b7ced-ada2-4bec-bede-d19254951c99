import { replaceSpecialCharacters } from '@onyma-ds/react';
import { ClientCompany, Filters } from '../types';

export const applyFilters = (data: ClientCompany[], filters: Filters) => {
  return data.filter((item) => {
    if (!filters.quickFilter) return true;
    const quickFilterClean = replaceSpecialCharacters(
      filters.quickFilter.trim().toLowerCase(),
    );
    const itemsQuickFilter = [
      replaceSpecialCharacters(item.name.toLocaleLowerCase()),
      item.createdAtFormatted,
    ];
    return itemsQuickFilter.some((itemFilter) =>
      itemFilter.includes(quickFilterClean),
    );
  });
};
