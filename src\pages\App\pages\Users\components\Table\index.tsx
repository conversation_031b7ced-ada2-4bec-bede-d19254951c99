import { useMemo } from 'react';
import { Table as TableDS } from '@onyma-ds/react';
import { Icons } from '@/components';
import { useUsers } from '../../contexts/users';
import { BadgeStatus, ButtonSort, ModalUser } from '..';
import * as SC from './styles';

export default function Table() {
  const { data, sort, onSortChange } = useUsers();

  const handleSort = (field: string) => {
    if (sort.field === field) {
      return onSortChange((prev) => ({
        ...prev,
        order: prev.order === 'asc' ? 'desc' : 'asc',
      }));
    }
    onSortChange((prev) => ({
      ...prev,
      field,
      order: 'asc',
    }));
  };

  const dataMapped = useMemo(() => {
    return data.map((item) => ({
      ...item,
      empresas: item.empresas?.map((empresa) => empresa.nome).join(', '),
      isActiveFormatted: item.ativo ? 'Sim' : 'Não',
      isActiveBadge: item.ativo ? 'success' : ('error' as 'success' | 'error'),
    }));
  }, [data]);

  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th title="Nome">
              <ButtonSort
                sort={sort.field === 'nome' ? sort.order : null}
                onClick={() => handleSort('nome')}
              >
                Nome
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="E-mail">
              <ButtonSort
                sort={sort.field === 'email' ? sort.order : null}
                onClick={() => handleSort('email')}
              >
                E-mail
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Empresa cliente">
              <ButtonSort
                sort={sort.field === 'empresaNome' ? sort.order : null}
                onClick={() => handleSort('empresaNome')}
              >
                Empresa cliente
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Perfis">
              <ButtonSort
                sort={sort.field === 'perfis' ? sort.order : null}
                onClick={() => handleSort('perfis')}
              >
                Perfis
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Ativo">
              <ButtonSort
                sort={sort.field === 'ativo' ? sort.order : null}
                onClick={() => handleSort('ativo')}
              >
                Ativo
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Data de cadastro">
              <ButtonSort
                sort={sort.field === 'dataCadastro' ? sort.order : null}
                onClick={() => handleSort('dataCadastro')}
              >
                Data de cadastro
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th title="Data últ. login">
              <ButtonSort
                sort={sort.field === 'dataUltimoLogin' ? sort.order : null}
                onClick={() => handleSort('dataUltimoLogin')}
              >
                Data últ. login
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th>
              <ModalUser mode="create">
                <SC.Trigger>
                  <Icons.Symbol
                    name="add"
                    size={16}
                  />{' '}
                  usuário
                </SC.Trigger>
              </ModalUser>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {dataMapped.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td>{item.nome}</TableDS.Td>
              <TableDS.Td>{item.email}</TableDS.Td>
              <TableDS.Td>{item.empresas}</TableDS.Td>
              <TableDS.Td>
                <SC.TdList>
                  {item.perfis.map((perfil) => (
                    <li key={`${item.id}-${perfil.idPerfil}`}>
                      - {perfil.nomePerfil}
                    </li>
                  ))}
                </SC.TdList>
              </TableDS.Td>
              <TableDS.Td>
                <BadgeStatus variant={item.isActiveBadge}>
                  {item.isActiveFormatted}
                </BadgeStatus>
              </TableDS.Td>
              <TableDS.Td>{item.dataCadastro}</TableDS.Td>
              <TableDS.Td>{item.dataUltimoLogin}</TableDS.Td>
              <TableDS.Td>
                <ModalUser
                  mode="edit"
                  userId={item.id}
                >
                  <SC.Trigger>
                    <Icons.Symbol
                      name="edit_square"
                      size={16}
                    />
                  </SC.Trigger>
                </ModalUser>
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
