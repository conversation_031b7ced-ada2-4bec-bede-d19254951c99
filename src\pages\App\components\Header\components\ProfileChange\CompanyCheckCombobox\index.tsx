import { Combobox, Text } from '@onyma-ds/react';
import { useState } from 'react';
import { Option } from '../CompanySelection/types';
import { ItemContainer } from '../SelectionCombobox/styles';
import { FilterActions, TriggerSpan } from './styles';
import { ComboboxCompanyProps, LabelAndValue } from './types';

export default function SelectCompany({
  company,
  selectedCompany,
  setSelectedCompany,
  placeholder,
  isFilter = false,
}: ComboboxCompanyProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSelect = (selectedCompany: LabelAndValue) => {
    setSelectedCompany(selectedCompany);
    setOpen(false);
  };

  const filteredComp = company.filter((comp) =>
    comp.label.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const selectedLabel = selectedCompany?.label || '';

  return (
    <Combobox.Root
      modal
      open={open}
      onOpenChange={setOpen}
    >
      <Combobox.Trigger
        placeholder={placeholder}
        data-placeholder={!selectedLabel}
        style={
          isFilter
            ? {
                background: selectedCompany?.value ? '#333' : '#fff',
                color: selectedCompany?.value ? '#fff' : '#333',
              }
            : {}
        }
      >
        <TriggerSpan>
          {selectedLabel ? selectedLabel : 'Selecione...'}
        </TriggerSpan>
      </Combobox.Trigger>
      <Combobox.Portal>
        <Combobox.Content
          placeholder=""
          style={{ width: '350px' }}
        >
          {isFilter && (
            <FilterActions>
              <p onClick={() => setSelectedCompany(null)}>Limpar filtro</p>
            </FilterActions>
          )}
          <Combobox.Command>
            <Combobox.Input
              crossOrigin=""
              placeholder="Buscar..."
              value={searchTerm}
              onValueChange={(e) => setSearchTerm(e)}
            />
            <Combobox.List>
              <Combobox.Empty>
                <Text>Nenhum resultado encontrado</Text>
              </Combobox.Empty>
              <Combobox.Viewport>
                <Combobox.Group>
                  {filteredComp.map((option: Option) => (
                    <Combobox.Item
                      key={option.value}
                      placeholder=""
                      value={option.label}
                      onSelect={() => handleSelect(option)}
                    >
                      <ItemContainer>
                        <img
                          src={
                            option.logo
                              ? option.logo[0] === '/'
                                ? `${import.meta.env.VITE_API_URL}/${option.logo}`
                                : option.logo
                              : 'https://placehold.co/24x24'
                          }
                        />
                        {option.label}
                      </ItemContainer>
                    </Combobox.Item>
                  ))}
                </Combobox.Group>
              </Combobox.Viewport>
            </Combobox.List>
          </Combobox.Command>
        </Combobox.Content>
      </Combobox.Portal>
    </Combobox.Root>
  );
}
