import { httpClient } from '@/services/httpClient';
import { RemoteCreateUserGroup } from './types';

export const remoteCreateUserGroup: RemoteCreateUserGroup = async ({
  name,
  description,
  requireCompany,
}) => {
  const response = await httpClient.post('/utilidades/perfil/create', {
    nome: name,
    requerEmpresa: requireCompany,
    descricao: description,
    icone: 'fa fa-user',
    homepage: '',
    menus: [],
  });
  return response.data;
};
