import { addMinutes, endOfDay, format, isAfter, startOfDay } from 'date-fns';

export type SecondStepProps = {
  schedule: Schedule[];
  setSchedule: React.Dispatch<React.SetStateAction<Schedule[]>>;
  setScheduleErrors: React.Dispatch<React.SetStateAction<string[]>>;
  scheduleErrors: string[];
};

interface TimeSlot {
  startTime: string;
  endTime: string;
}
export interface Schedule {
  day: string;
  available: boolean;
  timeSlots: TimeSlot[];
}

export const scheduleArray = [
  {
    day: 'SEG',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'TER',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'QUA',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'QUI',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'SEX',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  { day: 'SÁB', available: false, timeSlots: [] },
  { day: 'DOM', available: false, timeSlots: [] },
];

export function generateTimeOptions(interval: number): string[] {
  const times: string[] = [];
  let currentTime = startOfDay(new Date());
  const endTime = endOfDay(new Date());

  // Gera horários até o final do dia
  while (isAfter(endTime, currentTime)) {
    times.push(format(currentTime, 'HH:mm'));
    currentTime = addMinutes(currentTime, interval);
  }
  return times;
}

export function formatSchedule(schedule: Schedule[]) {
  const daysMap: { [key: string]: string } = {
    SEG: 'segunda',
    TER: 'terca',
    QUA: 'quarta',
    QUI: 'quinta',
    SEX: 'sexta',
    SÁB: 'sabado',
    DOM: 'domingo',
  };

  const formattedSchedule: { [key: string]: string[] } = {
    segunda: [],
    terca: [],
    quarta: [],
    quinta: [],
    sexta: [],
    sabado: [],
    domingo: [],
  };

  schedule.forEach((item) => {
    const dayKey = daysMap[item.day];
    if (item.available) {
      const validTimeSlots = item.timeSlots
        .filter(
          (slot) => slot.startTime.trim() !== '' && slot.endTime.trim() !== '',
        )
        .map((slot) => `${slot.startTime}-${slot.endTime}`);

      formattedSchedule[dayKey] = validTimeSlots;
    }
  });

  return formattedSchedule;
}
