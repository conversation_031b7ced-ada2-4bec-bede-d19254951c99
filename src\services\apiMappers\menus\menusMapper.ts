/* eslint-disable @typescript-eslint/no-explicit-any */
import { Menu } from '@/services/api/menus/remoteLoadMenus';

export const mapMenus = (menusData: any[]): Menu[] =>
  menusData.map((menu) => ({
    id: menu.uuid,
    title: menu.title || '',
    url: menu.url || '',
    type: menu.type || '',
    position: String(menu.position || ''),
    menus: mapMenus(menu.submenus),
    description: menu.description,
  }));
