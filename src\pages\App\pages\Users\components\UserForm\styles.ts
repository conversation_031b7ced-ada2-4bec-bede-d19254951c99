import { Switch } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

export const Fields = styled.fieldset`
  border: none;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    grid-template-columns: 1fr 1fr;
  }
`;

export const FieldsetAccess = styled.fieldset`
  border: none;

  legend {
    font-size: 0.875rem;
    font-weight: 600;
  }
`;

export const FieldsAccess = styled.div`
  margin-top: 1rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: 480px) {
    grid-template-columns: 1fr 1fr;
  }
`;

export const SwitchContainer = styled(Switch.Container)`
  font-size: 1.25rem;

  label {
    font-size: 0.875rem;
  }
`;

export const Buttons = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;

  button {
    display: grid;
    place-items: center;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
`;
