import { PageHeader } from '@/pages/App/components';
import { Icons } from '@/components';
import { Form } from './components';
import * as SC from './styles';

export default function AgendamentoEloparPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa.FaCalendarAlt size={24} />
          <PageHeader.Title>Agendamentos</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <Form />
    </SC.Container>
  );
}
