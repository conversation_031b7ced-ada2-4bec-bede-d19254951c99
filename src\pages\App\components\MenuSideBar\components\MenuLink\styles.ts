import { colorHexToRgba } from '@onyma-ds/react';
import styled, { css } from 'styled-components';

type ContainerProps = {
  $active: boolean;
};

export const Container = styled.li<ContainerProps>`
  color: ${({ theme }) => theme.colors.primary_extra_dark};
  display: block;
  list-style: none;
  border-left: 3px solid ${({ theme }) => theme.colors.white};

  /* &:last-child {
    margin-top: auto;
    border-top: 1px solid ${({ theme }) => theme.colors.gray_94};
  } */

  a {
    padding: 0.875rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.2s;

    p {
      margin-top: 4px;
    }

    &:hover,
    &:active {
      color: ${({ theme }) => theme.colors.secondary};
    }
  }

  ${({ theme, $active }) =>
    $active &&
    css`
      color: ${theme.colors.secondary};
      background-color: ${colorHexToRgba(
        theme.colors.secondary_extra_light,
        0.5,
      )};
      border-left-color: ${theme.colors.secondary};

      p {
        font-weight: 700;
      }
    `}
`;
