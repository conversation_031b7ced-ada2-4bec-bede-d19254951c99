/* eslint-disable react-hooks/exhaustive-deps */
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { ClientCompany } from '@/pages/App/pages/ClientCompanies/contexts/clientCompanies/types';
import { AvailableDate } from '@/services/api/calendar/remoteLoadAvailableDates';
import { Calendar } from '@/services/api/calendar/remoteLoadCalendars';
import { rolesId } from '@/utils/constants';
import { useToast } from '@onyma-ds/react';
import { useCallback, useEffect, useMemo, useState } from 'react';

type Params = {
  calendarId?: string;
  dateSelected?: Date;
};

export const useLoad = ({ calendarId, dateSelected }: Params) => {
  const { addToast } = useToast();
  const { clients, calendar: calendarApi } = useApi();
  const { user } = useAuth();

  const [messageToShow, setMessageToShow] = useState('');
  const [companies, setCompanies] = useState<ClientCompany[]>([]);
  const [calendars, setCalendars] = useState<Calendar[]>([]);
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);

  const loadCompanies = useCallback(async () => {
    try {
      const resultCompanies = await clients.loadClientCompanies();
      const allActiveCompanies = resultCompanies.result.filter(
        (company) => company.isActive,
      );
      if (!user.companyId) {
        setCompanies([]);
        return setMessageToShow(
          'Você precisa estar associado a uma empresa dentro do portal. Solicite ao seu gestor para cadastrar o seu usuário.',
        );
      }
      if (user.currentRole.id === rolesId.administradorDoSistema) {
        return setCompanies(allActiveCompanies);
      }
      setCompanies(
        allActiveCompanies.filter((company) => company.id === user.companyId),
      );
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data.title || 'Carregamento das empresas',
        description:
          error.response?.data.message || 'Erro ao carregar as empresas',
        timeout: 5000,
      });
    }
  }, [addToast, clients, user]);

  const loadCalendars = useCallback(async () => {
    try {
      const resultCalendars = await calendarApi.loadCalendars();
      setCalendars(resultCalendars.result);
    } catch (error) {
      setCalendars([]);
      addToast({
        type: 'error',
        title: error.response?.data.title || 'Carregamento das agendas',
        description:
          error.response?.data.message || 'Erro ao carregar as agendas',
        timeout: 5000,
      });
    }
  }, [calendarApi, addToast]);

  const loadAvailableDates = useCallback(
    async (calendarIdToFind: string) => {
      try {
        const calendar = calendars.find(
          (calendar) => calendar.id.toString() === calendarIdToFind,
        );
        if (!calendar) return;
        const resultAvailableDates = await calendarApi.loadAvailableDates({
          codigoAgenda: calendar.codigo,
          codigoSoc: user.socCode,
        });
        setAvailableDates(resultAvailableDates.result);
      } catch (error) {
        addToast({
          type: 'error',
          title: error.response?.data.title || 'Carregamento das datas',
          description:
            error.response?.data.message ||
            'Erro ao carregar as datas da agenda',
          timeout: 5000,
        });
      }
    },
    [addToast, calendarApi, calendars],
  );

  useEffect(() => {
    Promise.all([loadCompanies(), loadCalendars()]);
  }, [loadCompanies, loadCalendars]);

  useEffect(() => {
    if (calendarId) {
      loadAvailableDates(calendarId);
    }
  }, [calendarId, loadAvailableDates]);

  const companiesOptions = useMemo(() => {
    const optionsMapped = companies.map((company) => ({
      label: company.name,
      value: company.id,
    }));
    return optionsMapped;
  }, [companies]);

  const calendarOptions = useMemo(() => {
    const options = calendars
      .filter((calendar) =>
        calendar.empresaCliente.some(
          (company) => company.id === user.companyId,
        ),
      )
      .map((calendar) => ({
        label: calendar.nome || calendar.endereco,
        value: calendar.id.toString(),
      }));
    return options;
  }, [calendars, user]);

  const dateOptions = useMemo(() => {
    return availableDates.map((date) => date.dateTyped);
  }, [availableDates]);

  const hoursOptions = useMemo(() => {
    if (!dateSelected) return [];
    const dateSelectedFormatted = dateSelected.toLocaleDateString('pt-BR');
    return availableDates
      .filter((date) => dateSelectedFormatted.includes(date.data))
      .map((date) => ({
        label: date.horario,
        value: date.horario,
      }));
  }, [dateSelected, availableDates]);

  return {
    messageToShow,
    companies,
    companiesOptions,
    calendarOptions,
    calendars,
    dateOptions,
    hoursOptions,
    availableDates,
  };
};
