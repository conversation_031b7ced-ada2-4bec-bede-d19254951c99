import { Link, useMatch } from 'react-router-dom';
import * as SC from './styles';
import { Props } from './types';

export default function MenuLink({ href, title, children }: Props) {
  const match = useMatch({ path: href, end: false });

  return (
    <SC.Container $active={!!match}>
      <Link
        to={href}
        title={title}
      >
        {children}
      </Link>
    </SC.Container>
  );
}
