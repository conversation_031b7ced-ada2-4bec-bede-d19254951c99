import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const Description = styled.article`
  color: ${({ theme }) => theme.colors.gray_40};
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const ActionLink = styled(Link)`
  width: fit-content;
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.secondary};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.secondary};
  border-radius: 4px;
  padding: 4px 8px;

  &:hover {
    color: ${({ theme }) => theme.colors.white};
    background-color: ${({ theme }) => theme.colors.secondary};
  }
`;

export const Container = styled.div`
  height: 100%;
  text-align: center;
  color: ${({ theme }) => theme.colors.primary_dark};
  background-color: ${({ theme }) => theme.colors.white};
  box-shadow: 0px 1px 2px 1px rgba(154, 154, 204, 0.22);
  padding: 1rem;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  &:hover {
    color: ${({ theme }) => theme.colors.white};
    background-color: ${({ theme }) => theme.colors.primary};

    ${Description} {
      color: ${({ theme }) => theme.colors.white};
    }
  }
`;
