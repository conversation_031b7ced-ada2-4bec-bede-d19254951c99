import React from 'react';
import { InputText, Pagination } from '@onyma-ds/react';
import { Spinner } from '@/components';
import { useClientCompanies } from '../../contexts/clientCompanies';
import { Table } from '..';
import * as SC from './styles';

export default function Companies() {
  const { loading, pagination, filters, onPaginationChange, onFiltersChange } =
    useClientCompanies();

  const handleQuickFilterChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { value } = event.target;
    onPaginationChange((prev) => ({ ...prev, page: 1 }));
    onFiltersChange((prev) => ({ ...prev, quickFilter: value }));
  };

  const handlePageChange = (page: number) => {
    onPaginationChange((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    onPaginationChange((prev) => ({ ...prev, page: 1, perPage: itemsPerPage }));
  };

  if (loading) {
    return (
      <SC.ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerLoading>
    );
  }

  return (
    <SC.Container>
      <SC.QuickFilterBox>
        <InputText
          placeholder="Pesquisar..."
          value={filters.quickFilter}
          onChange={handleQuickFilterChange}
        />
      </SC.QuickFilterBox>
      <SC.TableBox>
        <Table />
      </SC.TableBox>
      <SC.PaginationBox>
        <Pagination
          currentPage={pagination.page}
          itemsPerPage={pagination.perPage}
          totalItems={pagination.totalItems}
          itemsPerPageOptions={[10, 20, 30, 50, 100]}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      </SC.PaginationBox>
    </SC.Container>
  );
}
