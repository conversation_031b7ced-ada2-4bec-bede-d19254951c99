import { Pagination } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import Table from '../Table';

import { Pagination as PaginationType } from '@/@types/Pagination';
import { useApi } from '@/contexts/api';

import { IFile } from '@/services/api/file/remoteGetAllFiles/types';
import { useFilters } from '../Contexts/useFilters';
import { Filters } from '../Filters';
import * as SC from './styles';
import { applyPagination } from './utils';
import { format } from 'date-fns';
import { useAuth } from '@/contexts/auth';

export default function DownloadManagementPage() {
  const { user } = useAuth();

  const {
    file: { loadFiles },
  } = useApi();

  const { companiesFilter, profileFilter, endDateFilter, startDateFilter } =
    useFilters();

  const [fileToEdit, setFileToEdit] = useState<IFile>();
  const [allFiles, setAllFiles] = useState<IFile[]>([]);
  const [pagination, setPagination] = useState<PaginationType>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const isUserAdmin = user?.currentRole.name.includes('Administrador');

  const { data, isLoading } = useQuery({
    queryKey: [
      'fetch-all-files',
      companiesFilter,
      profileFilter,
      endDateFilter,
      startDateFilter,
    ],
    queryFn: () =>
      loadFiles({
        idEmpresaCliente: isUserAdmin
          ? companiesFilter && companiesFilter.value
          : (user.companyId as string),
        idPerfil: isUserAdmin
          ? profileFilter?.map((profile) => profile.value).join(',')
          : (user.currentRole.id as string),
        dataInicio:
          startDateFilter && format(startDateFilter as Date, 'dd/MM/yyyy'),
        dataFim: endDateFilter && format(endDateFilter as Date, 'dd/MM/yyyy'),
      }),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: 1,
      perPage: itemsPerPage,
    }));
  };

  useEffect(() => {
    setAllFiles(data?.result ?? []);
    setPagination((prev) => ({
      ...prev,
      totalItems: data?.result.length ?? 0,
    }));
    return;
  }, [data]);

  useEffect(() => {
    if (!data) return;

    setAllFiles(() => applyPagination(data?.result, pagination));
  }, [pagination, data]);

  const handleSelectAgenda = (selectedFile: IFile) => {
    setFileToEdit(selectedFile);
  };

  return (
    <>
      <SC.Container>
        <SC.QuickFilterBox>
          <Filters />
        </SC.QuickFilterBox>
        <SC.TableBox>
          <Table
            isLoading={isLoading}
            files={allFiles}
            fileToEdit={fileToEdit}
            handleSelectAgenda={handleSelectAgenda}
          />
        </SC.TableBox>
        <SC.PaginationBox>
          <Pagination
            currentPage={pagination.page}
            itemsPerPage={pagination.perPage}
            totalItems={pagination.totalItems}
            itemsPerPageOptions={[10, 20, 30, 50, 100]}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </SC.PaginationBox>
      </SC.Container>
    </>
  );
}
