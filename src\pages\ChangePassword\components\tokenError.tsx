import { But<PERSON> } from '@/components/ui/button';
import { TriangleAlert } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function ExpiredTokenError() {
  return (
    <div className="flex flex-col gap-10 w-[400px]">
      <div className="flex flex-col gap-4 items-center">
        <TriangleAlert
          size={40}
          className="text-red-400"
        />
        <h1 className="text-center text-2xl font-medium">
          O link para recuperação de senha expirou. Por favor, solicite um novo
          link para continuar.
        </h1>
      </div>
      <Link to="/esqueci-minha-senha">
        <Button
          type="button"
          className="w-full"
        >
          Solicitar novo link
        </Button>
      </Link>
    </div>
  );
}
