import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@onyma-ds/react';
import { useMutation } from '@tanstack/react-query';
import { Controller, useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { RequestAccessFormType, requestAccessSchema } from './validateForm';

export default function Form() {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const {
    auth: { requestAccess },
  } = useApi();

  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<RequestAccessFormType>({
    resolver: zodResolver(requestAccessSchema),
    defaultValues: {
      name: '',
      company: '',
      email: '',
      role: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: requestAccess,
    onSuccess: () => {
      addToast({
        title: 'Solicitação enviada com sucesso',
        description: 'Sua solicitação foi enviada com sucesso',
        type: 'success',
      });
      navigate('/login', { replace: true });
    },
    onError: (error: Error) => {
      addToast({
        title: 'Erro ao solicitar acesso. Tente novamente mais tarde.',
        description: error.message,
        type: 'error',
      });
    },
  });

  const onSubmit = (data: RequestAccessFormType) => {
    mutate(data);
  };

  return (
    <form
      className="flex flex-col gap-8 w-[360px]"
      noValidate
      onSubmit={handleSubmit(onSubmit)}
    >
      <h1 className="text-2xl">
        Solicitação de acesso <br />{' '}
        <b className="text-primary">Portal BenCorp</b>
      </h1>
      <Controller
        name="name"
        control={control}
        render={({ field }) => (
          <Input
            label="Nome completo"
            placeholder="Nome completo"
            errorMessage={errors.name?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="company"
        control={control}
        render={({ field }) => (
          <Input
            label="Empresa"
            placeholder="Empresa"
            errorMessage={errors.company?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="email"
        control={control}
        render={({ field }) => (
          <Input
            label="E-mail"
            type="email"
            placeholder="E-mail"
            errorMessage={errors.email?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="role"
        control={control}
        render={({ field }) => (
          <Input
            label="Cargo"
            placeholder="Cargo"
            errorMessage={errors.role?.message}
            {...field}
          />
        )}
      />

      <Button
        type="submit"
        color="white"
        disabled={isPending}
        isLoading={isPending}
      >
        Enviar
      </Button>
      <div className="text-center text-sm text-gray-700">
        ou{' '}
        <Link
          to="/login"
          className="hover:underline"
        >
          Entrar
        </Link>
      </div>
    </form>
  );
}
