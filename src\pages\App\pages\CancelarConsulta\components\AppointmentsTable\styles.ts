import styled, { DefaultTheme } from 'styled-components';
import { Button, Table, Text } from '@onyma-ds/react';

export const SquareButton = styled(Button)`
  aspect-ratio: 1;
  padding: 0;
  width: 100%;
  height: 100%;
  max-width: 1.5rem;
  max-height: 1.5rem;
  display: grid;
  place-items: center;
`;

export const AppointmentsTable = styled(Table.Root)`
  th:last-child {
    width: 2rem;
  }
`;

export const Centered = styled.div`
  display: grid;
  place-items: center;
`;

export const CenteredText = styled(Text)<{
  $color?: keyof DefaultTheme['colors'];
}>`
  text-align: center;
  color: ${({ theme, $color = 'black' }) => theme.colors[$color]};
`;
