/* eslint-disable react-hooks/exhaustive-deps */
import { Icons } from '@/components';
import { Checkbox, SelectBox } from '@onyma-ds/react';
import * as SC from './styles';
import { generateTimeOptions } from '../../utils';
import { useEffect } from 'react';
import { ErrorMessage } from '@/pages/Login/components/LoginForm/styles';

interface TimeSlot {
  startTime: string;
  endTime: string;
}

interface ScheduleItemProps {
  day: string;
  available: boolean;
  timeSlots: TimeSlot[];
  setScheduleErrors: React.Dispatch<React.SetStateAction<string[]>>;
  scheduleErrors: string[];
  onScheduleChange: (
    day: string,
    timeSlots: TimeSlot[],
    available: boolean,
  ) => void;
}

export function ScheduleItem({
  day,
  available,
  timeSlots,
  onScheduleChange,
  scheduleErrors,
  setScheduleErrors,
}: ScheduleItemProps) {
  const timeOptions = generateTimeOptions(30);

  const handleAddSlot = () => {
    onScheduleChange(
      day,
      [...timeSlots, { startTime: '', endTime: '' }],
      available,
    );
  };

  const handleRemoveSlot = (index: number) => {
    const newTimeSlots = timeSlots.filter((_, i) => i !== index);
    onScheduleChange(day, newTimeSlots, available);
  };

  const handleChange = (
    index: number,
    field: 'startTime' | 'endTime',
    value: string,
  ) => {
    const newTimeSlots = [...timeSlots];
    newTimeSlots[index][field] = value;
    onScheduleChange(day, newTimeSlots, available);
  };

  const toggleAvailability = () => {
    onScheduleChange(
      day,
      available ? [] : [{ startTime: '09:00', endTime: '18:00' }],
      !available,
    );
  };

  const validateTimeSlots = () => {
    const newErrors = timeSlots.map((slot) => {
      if (slot.startTime && !slot.endTime) {
        return 'Data final é obrigatória.';
      }
      if (!slot.startTime && slot.endTime) {
        return 'Data inicial é obrigatória.';
      }
      return '';
    });
    setScheduleErrors(newErrors);
  };

  useEffect(() => {
    validateTimeSlots();
  }, [timeSlots]);

  return (
    <SC.Container>
      <SC.CheckboxContainer>
        <Checkbox
          id={day}
          name={day}
          value={day}
          checked={available}
          onChange={toggleAvailability}
        />
        <Checkbox.Label>{day}</Checkbox.Label>
      </SC.CheckboxContainer>

      <SC.WrapperContainer>
        {available ? (
          timeSlots.map((slot, index) => (
            <>
              <SC.HourContainer key={index}>
                <SelectBox
                  options={timeOptions.map((time) => ({
                    label: time,
                    value: time,
                  }))}
                  onSelect={(option) =>
                    handleChange(index, 'startTime', option?.value || '')
                  }
                  optionSelected={{
                    label: slot.startTime,
                    value: slot.startTime,
                  }}
                />

                <span>-</span>
                <SelectBox
                  options={timeOptions.map((time) => ({
                    label: time,
                    value: time,
                  }))}
                  onSelect={(option) =>
                    handleChange(index, 'endTime', option?.value || '')
                  }
                  optionSelected={{
                    label: slot.endTime,
                    value: slot.endTime,
                  }}
                />
                <SC.TriggerDelete
                  disabled={timeSlots.length === 1}
                  onClick={() => handleRemoveSlot(index)}
                >
                  <Icons.Symbol
                    name="close"
                    size={16}
                  />
                </SC.TriggerDelete>
              </SC.HourContainer>
              {scheduleErrors[index] && (
                <ErrorMessage>{scheduleErrors[index]}</ErrorMessage>
              )}
            </>
          ))
        ) : (
          <span>Indisponível</span>
        )}
      </SC.WrapperContainer>
      <SC.Trigger
        onClick={handleAddSlot}
        type="button"
      >
        <Icons.Symbol
          name="add"
          size={16}
        />
      </SC.Trigger>
    </SC.Container>
  );
}

export default ScheduleItem;
