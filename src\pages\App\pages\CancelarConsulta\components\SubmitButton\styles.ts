import styled, { css } from 'styled-components';
import { Button } from '@onyma-ds/react';

export const SubmitButton = styled(Button)<{
  $isSubmitting?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacings.nano};

  ${({ $isSubmitting }) =>
    $isSubmitting &&
    css`
      /**
       * NOTE: While submitting, do not allow the button to be the target of 
       * pointer events, in order to prevent multiple form submitions from ocurring 
       * at the same time.
       */
      pointer-events: none;
    `}
`;
