import { useAuth } from '@/contexts/auth';
import { Heading, Text } from '@onyma-ds/react';
import Accordion from './Components/Accordion';
import { Title, VideoContainer } from './Components/Accordion/styles';
import VideoPlayer from './Components/Player';
import { faqs, FaqIbm } from './faq';
import { AccordionWrapper, ChildrenWrapper, Container } from './styles';
import { Faq } from './types';

export default function FaqPage() {
  const { user } = useAuth();

  const isIbm = user.companyId === import.meta.env.VITE_IBM_ID; //IBM ID
  const currentFaq: Faq[] = isIbm ? FaqIbm : faqs;

  return (
    <Container>
      <Heading>Perguntas frequentes</Heading>
      <AccordionWrapper>
        {currentFaq.map((faq) => (
          <Accordion
            key={faq.summary}
            title={faq.summary}
          >
            {Array.isArray(faq.children) ? (
              faq.children.map((child) => (
                <ChildrenWrapper key={child.title}>
                  <Accordion title={child.title}>
                    {child.videoUrl ? (
                      <VideoContainer>
                        <VideoPlayer url={child.videoUrl} />
                      </VideoContainer>
                    ) : (
                      <Text>{child.description}</Text>
                    )}
                  </Accordion>
                </ChildrenWrapper>
              ))
            ) : (
              <ChildrenWrapper>
                <Title className="title-summary">{faq.children.title}</Title>
                {faq.children.videoUrl && (
                  <VideoContainer>
                    <VideoPlayer url={faq.children.videoUrl} />
                  </VideoContainer>
                )}
              </ChildrenWrapper>
            )}
          </Accordion>
        ))}
      </AccordionWrapper>
    </Container>
  );
}
