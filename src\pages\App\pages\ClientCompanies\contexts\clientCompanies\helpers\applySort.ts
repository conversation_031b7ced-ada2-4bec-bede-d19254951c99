import { ClientCompany, Sort } from '../types';

export const applySort = (data: ClientCompany[], sort: Sort) => {
  if (sort.field === 'name') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.name < b.name ? -1 : 1;
      }
      return a.name > b.name ? -1 : 1;
    });
  }

  if (sort.field === 'isActive') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.isActive ? -1 : 1;
      }
      return b.isActive ? -1 : 1;
    });
  }

  if (sort.field === 'includedBenSaude') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.includedBenSaude ? -1 : 1;
      }
      return b.includedBenSaude ? -1 : 1;
    });
  }

  if (sort.field === 'createdAt') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.createdAt.getTime() < b.createdAt.getTime() ? -1 : 1;
      }
      return a.createdAt.getTime() > b.createdAt.getTime() ? -1 : 1;
    });
  }

  return data;
};
