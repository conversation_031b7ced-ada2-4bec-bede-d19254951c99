import styled from 'styled-components';

type IStyledLabel = {
  disabled: boolean;
};

export const Container = styled.section`
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const FormWrapper = styled.form`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
  padding-bottom: 4rem;

  gap: 3rem;

  .button {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
  }
`;

export const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const TextInput = styled.input`
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 0.875rem;
  color: #aaa;

  &::placeholder {
    color: #ccc;
  }
`;

export const StyledLabel = styled.label<IStyledLabel>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  gap: 0.5rem;

  span {
    font-size: 0.875rem;
  }

  height: 2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;

  ${({ disabled }) =>
    disabled &&
    `
    background-color: #ccc;
    cursor: not-allowed;
  `}

  &:hover {
    background-color: ${({ theme, disabled }) =>
      disabled ? '' : theme.colors.primary};
  }

  & svg {
    font-size: 20px;
  }
`;

export const StyledInput = styled.input`
  display: none;
`;

export const FileUploadWrapper = styled.div`
  display: flex;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0.4rem 0.5rem;
  background-color: #f9f9f9;

  width: 100%;
  margin-bottom: 0.25rem;

  &:focus-within {
    border-color: #007bff;
  }
`;
