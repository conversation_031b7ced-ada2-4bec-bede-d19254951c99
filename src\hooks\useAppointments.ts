import { useQuery } from '@tanstack/react-query';
import { useApi } from '@/contexts/api';

type UseAppointmentsParams = {
  startDate: Date;
  endDate: Date;
  calendar: string;
};

export const useAppointments = ({
  startDate,
  endDate,
  calendar,
}: UseAppointmentsParams) => {
  const {
    calendar: { loadAppointments },
  } = useApi();
  return useQuery({
    queryKey: ['appointments', startDate, endDate, calendar],
    queryFn: () =>
      loadAppointments({
        dataInicial: startDate,
        dataFinal: endDate,
        codigoUsuarioAgenda: calendar,
      }),
  });
};
