import { Icons } from '@/components';

export type MenuType = {
  name: string;
  value: string;
  url: string;
};

export const moduleOptions = [
  {
    name: '<PERSON><PERSON>cio',
    value: 'START',
    url: '/app/home',
    icon: <Icons.RAIo5.IoHomeOutline size={20} />,
  },
  {
    name: 'Apps',
    value: 'APPS',
    url: '/app/apps',
    icon: <Icons.RABs.BsFolder size={20} />,
  },
  {
    name: 'Indicadores',
    value: 'INDICADORES',
    url: '/app/indicadores',
    icon: <Icons.RABs.BsBarChart size={20} />,
  },
  {
    name: 'FAQ',
    value: 'FAQ',
    url: '/app/faq',
    icon: <Icons.RAAi.AiOutlineQuestionCircle size={20} />,
  },
  {
    name: 'Sistema',
    value: 'SISTEMA',
    url: '/app/sistema',
    icon: <Icons.RAGr.GrSystem size={20} />,
  },
];
