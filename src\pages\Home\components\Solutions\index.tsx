import { Heading, Text } from '@onyma-ds/react';
import * as SC from './styles';

export default function Solutions() {
  return (
    <SC.Container id="solucoes">
      <SC.SolutionsBox>
        <SC.Solutions>
          <Heading
            as="h2"
            type="heading_02"
          >
            Soluções
          </Heading>
          <ul>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Indicadores de Saúde
              </Heading>
              <Text>
                Acesse com facilidade e praticidade, os seus indicadores de
                saúde
              </Text>
            </li>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Chamados
              </Heading>
              <Text>
                Realize aqui a abertura dos seus chamados, com total praticidade
              </Text>
            </li>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Solicitação de treinamentos
              </Heading>
              <Text>
                Centralize suas solicitações de treinamento pelo Portal BenCorp
              </Text>
            </li>
          </ul>
        </SC.Solutions>
        <SC.ImageBox>
          <img
            src="/imgs/validation.png"
            alt="Uma pessoa recebendo solicitações no celular"
            loading="lazy"
          />
        </SC.ImageBox>
      </SC.SolutionsBox>
      <SC.SolutionsBox>
        <SC.Solutions>
          <ul>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Indicadores Rede Credenciada
              </Heading>
              <Text>
                Acompanhe seus indicadores de credenciamento mensalmente e fique
                atualizado
              </Text>
            </li>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Indicadores Situacional de Exames
              </Heading>
              <Text>
                Acompanhe o situacional dos seus colaboradores de uma forma
                prática
              </Text>
            </li>
            <li>
              <Heading
                as="h4"
                type="heading_04"
              >
                Indicadores de Campanha
              </Heading>
              <Text>Seu situacional de campanha atualizado em um só lugar</Text>
            </li>
          </ul>
        </SC.Solutions>
        <SC.ImageBox>
          <img
            src="/imgs/marketer.png"
            alt="Uma pessoa apontando o dedo para um quadro de post-its"
            loading="lazy"
          />
        </SC.ImageBox>
      </SC.SolutionsBox>
    </SC.Container>
  );
}
