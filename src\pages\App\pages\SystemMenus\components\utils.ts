import { LabelAndValue } from '@/pages/App/components/Header/components/ProfileChange/CompanyCheckCombobox/types';
import { Menu } from '@/services/api/menus/remoteLoadMenus';

export const findRecursivesMenu = (
  allMenus: Menu[],
  currentMenuId: string,
): LabelAndValue[] => {
  const result: LabelAndValue[] = [];

  const findSubmenus = (items: Menu[]) => {
    items.forEach((item) => {
      if (
        item.id !== currentMenuId &&
        item.uuid !== currentMenuId &&
        item.type === 5
      ) {
        result.push({ label: item.title, value: item.id ?? item.uuid });
      }
      if (!item.submenus) return;
      if (item.submenus.length > 0) {
        findSubmenus(item.submenus);
      }
    });
  };

  findSubmenus(allMenus ?? []);
  return result;
};
