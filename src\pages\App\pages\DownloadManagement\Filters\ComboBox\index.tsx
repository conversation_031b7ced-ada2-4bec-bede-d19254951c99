import { LabelAndValue } from '@/pages/App/components/Header/components/ProfileChange/CompanyCheckCombobox/types';
import { ErrorMessage } from '@/pages/Login/components/LoginForm/styles';
import { Checkbox, Combobox, Text } from '@onyma-ds/react';
import { Dispatch, SetStateAction, useState } from 'react';
import { VscChevronDown } from 'react-icons/vsc';
import {
  CheckboxContainer,
  Container,
  FilterActions,
  Span,
  TriggerSpan,
} from './styles';

type ComboboxProps = {
  label?: string;
  errorMessage?: string;
  selectedItems: { label: string; value: string }[];
  options: LabelAndValue[];
  setSelectedItems: Dispatch<
    SetStateAction<{ label: string; value: string }[]>
  >;
};

export function ComboboxFilter({
  label,
  errorMessage,
  selectedItems,
  options,
  setSelectedItems,
}: ComboboxProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSelect = (selectedOption: LabelAndValue) => {
    const isSelected = selectedItems.some(
      (item) => item.value === selectedOption.value,
    );

    if (isSelected) {
      setSelectedItems((prev) =>
        prev.filter((item) => item.value !== selectedOption.value),
      );
    } else {
      setSelectedItems((prev) => [selectedOption, ...prev]);
    }
  };

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <Container>
      <Combobox.Root
        open={open}
        onOpenChange={setOpen}
      >
        <Combobox.Trigger
          placeholder=""
          style={{
            background: selectedItems.length > 0 ? '#333333' : '',
            color: selectedItems.length > 0 ? '#fff' : '',
          }}
        >
          <TriggerSpan>
            {label}
            <VscChevronDown />
          </TriggerSpan>
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content
            placeholder=""
            style={{ width: '350px' }}
          >
            <FilterActions>
              <Span>Filtro por {label}</Span>
              <p onClick={() => setSelectedItems([])}>Limpar filtro</p>
            </FilterActions>
            <Combobox.Command>
              <Combobox.Input
                crossOrigin=""
                placeholder="Buscar..."
                value={searchTerm}
                onValueChange={(e) => setSearchTerm(e)}
              />
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Group>
                    {filteredOptions.map((option) => (
                      <CheckboxContainer key={option.label}>
                        <Combobox.Item
                          placeholder=""
                          value={option.label}
                          onSelect={() => handleSelect(option)}
                        >
                          <Checkbox
                            id={option.value}
                            name={option.label}
                            value={option.value}
                            onChange={() => handleSelect(option)}
                            checked={selectedItems.some(
                              (selectedItem) =>
                                selectedItem.value === option.value,
                            )}
                          />
                          <Checkbox.Label>{option.label}</Checkbox.Label>
                        </Combobox.Item>
                      </CheckboxContainer>
                    ))}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
        {!!errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
      </Combobox.Root>
    </Container>
  );
}
