import { Button, Table } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.div`
  flex: 1;
  min-width: 900px;
  padding: 0 1rem;
  position: absolute;
  inset: 0;
`;

export const Root = styled(Table.Root)`
  height: min-content;

  td {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  th,
  td {
    white-space: nowrap;

    &:last-child {
      width: 6rem;
      text-align: right;
    }
  }
`;

export const SquareButton = styled(Button)`
  aspect-ratio: 1;
  padding: 0;
  width: 100%;
  height: 100%;
  max-width: 1.5rem;
  max-height: 1.5rem;
  display: grid;
  place-items: center;
`;

export const Trigger = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  font-size: 0.75rem;
  padding: 4px;
  margin-left: auto;
  border-radius: 4px;
  transition: filter 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    filter: brightness(0.9);
  }
`;
