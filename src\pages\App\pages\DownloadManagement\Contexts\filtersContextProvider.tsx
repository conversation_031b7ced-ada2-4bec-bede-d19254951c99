import { LabelAndValue, LabelAndValueAndLogo } from '@/@types/LabelAndValue';
import {
  Dispatch,
  PropsWithChildren,
  SetStateAction,
  createContext,
  useState,
} from 'react';

type FilterContextType = {
  profileFilter: LabelAndValue[];
  fileTypeFilter: LabelAndValue[];
  endDateFilter: Date | undefined;
  startDateFilter: Date | undefined;
  companiesFilter: LabelAndValueAndLogo;
  setProfileFilter: Dispatch<SetStateAction<LabelAndValue[]>>;
  setEndDateFilter: Dispatch<SetStateAction<Date | undefined>>;
  setStartDateFilter: Dispatch<SetStateAction<Date | undefined>>;
  setFileTypeFilter: Dispatch<SetStateAction<LabelAndValue[]>>;
  setCompaniesFilter: Dispatch<SetStateAction<LabelAndValueAndLogo>>;
};

export const FiltersContext = createContext<FilterContextType | null>(null);

export function FiltersContextProvider({ children }: PropsWithChildren) {
  const [endDateFilter, setEndDateFilter] = useState<Date>();
  const [startDateFilter, setStartDateFilter] = useState<Date>();
  const [profileFilter, setProfileFilter] = useState<LabelAndValue[]>([]);
  const [fileTypeFilter, setFileTypeFilter] = useState<LabelAndValue[]>([]);
  const [companiesFilter, setCompaniesFilter] = useState<LabelAndValueAndLogo>(
    {} as LabelAndValueAndLogo,
  );

  return (
    <FiltersContext.Provider
      value={{
        companiesFilter,
        setCompaniesFilter,
        profileFilter,
        setProfileFilter,
        fileTypeFilter,
        setFileTypeFilter,
        startDateFilter,
        setStartDateFilter,
        endDateFilter,
        setEndDateFilter,
      }}
    >
      {children}
    </FiltersContext.Provider>
  );
}
