import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadBIPage } from './types';

export const remoteLoadBIPage: RemoteLoadBIPage = async (params) => {
  const response = await httpClient.get('/bi/pagina/fetch', {
    params: {
      id: params.id,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: {
      id: response.data.result.id,
      biId: response.data.result.biId,
      powerBiName: response.data.result.powerBiNome,
      name: response.data.result.nome,
      description: response.data.result.descricao,
      biIdObject: response.data.result.biIdObject,
    },
  };
};
