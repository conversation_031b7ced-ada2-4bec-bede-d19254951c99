import styled, { keyframes } from 'styled-components';
import { theme } from '@/styles/theme';

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

export type ColorType = keyof typeof theme.colors;

type ContainerProps = {
  size: number;
  color: ColorType;
};

export const Container = styled.div<ContainerProps>`
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left-color: ${({ color }) => theme.colors[color]};
  border-radius: 50%;
  width: ${({ size }) => `${size}px`};
  height: ${({ size }) => `${size}px`};
  animation: ${spin} 1s linear infinite;
`;
