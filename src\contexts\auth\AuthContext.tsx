/* eslint-disable react-hooks/exhaustive-deps */
import { PropsWithChildren, createContext, useCallback, useState } from 'react';
import { remoteLogin } from '@/services/api/auth/remoteLogin';
import { AuthStorageService } from '@/services/storage/AuthStorageService';
import { AuthContextData, AuthUser, LoginFunction } from './types';
import { useApi } from '../api';
import { useMutation } from '@tanstack/react-query';

export const AuthContext = createContext<AuthContextData | null>(null);

export const AuthContextProvider = ({ children }: PropsWithChildren) => {
  const {
    clients: { updateUserRole },
  } = useApi();
  const [user, setUser] = useState<AuthUser | null>(
    AuthStorageService.getUser(),
  );

  const setUserData = useCallback((data: AuthUser) => {
    setUser(data);
    AuthStorageService.setUser(data);
  }, []);

  const { mutateAsync } = useMutation({
    mutationFn: (data: { company: string | null; role: string; id: string }) =>
      updateUserRole({ company: data.company, role: data.role, uuid: data.id }),
  });

  const login: LoginFunction = useCallback(async (params) => {
    const apiResult = await remoteLogin({
      username: params.username,
      password: params.password,
    });

    const lastLoggedCompany = apiResult.result.currentCompany ?? null;

    setUser({
      uuid: apiResult.result.uuid,
      fullName: apiResult.result.fullName,
      email: apiResult.result.email,
      currentRole: apiResult.result.currentRole,
      socRegister: apiResult.result.socRegister,
      socCode: apiResult.result.socCode,
      companies: apiResult.result.companies,
      isFirstLogin: apiResult.result.isFirstLogin,
      roles: apiResult.result.roles,
      companyId: lastLoggedCompany?.id,
      companyName: lastLoggedCompany?.nome,
    });

    await mutateAsync({
      company: apiResult.result.companies[0]?.id,
      role: apiResult.result.currentRole.id,
      id: apiResult.result.uuid,
    });

    AuthStorageService.setAccessToken(apiResult.result.accessToken);
    AuthStorageService.setMicrosoftAccessToken(apiResult.microsoftAccessToken);
    AuthStorageService.setRefreshToken(apiResult.result.refreshToken);
    AuthStorageService.setAuthKeepSession(params.keepSession);
    AuthStorageService.setUser({
      ...apiResult.result,
      companyId: lastLoggedCompany?.id,
      companyName: lastLoggedCompany?.nome,
    });
    return apiResult;
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    AuthStorageService.clear();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user: user as AuthUser,
        login,
        logout,
        setUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
