/* eslint-disable react-hooks/exhaustive-deps */
import {
  PropsWithChildren,
  createContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { Pagination } from '@/@types/Pagination';
import { User } from '@/services/api/users/remoteLoadUsers';
import { helpers } from './helpers';
import { Filters, Sort, UsersContextData } from './types';

export const UsersContext = createContext<UsersContextData | null>(null);

export const UsersContextProvider = ({ children }: PropsWithChildren) => {
  const { addToast } = useToast();
  const { users: usersApi } = useApi();

  const [loading, setLoading] = useState({ listing: false });
  const [users, setUsers] = useState<User[]>([]);
  const [filters, setFilters] = useState<Filters>({});
  const [sort, setSort] = useState<Sort>({ field: 'nome', order: 'asc' });
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const loadUsers = async () => {
    try {
      setLoading((prev) => ({ ...prev, listing: true }));
      const usersResult = await usersApi.loadUsers();
      setUsers(usersResult.result);
      setPagination((prev) => ({
        ...prev,
        totalItems: usersResult.result.length,
      }));
    } catch (error) {
      setUsers([]);
      addToast({
        type: 'error',
        title: error.response?.data?.title || 'Carregamento os Usuários',
        description:
          error.response?.data?.message || 'Erro ao carregar os usuários',
        timeout: 5000,
      });
    } finally {
      setLoading((prev) => ({ ...prev, listing: false }));
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const usersMapped = useMemo(() => {
    return helpers.applyPagination(
      helpers.applyFilters(helpers.applySort(users, sort), filters),
      pagination,
    );
  }, [users, filters, pagination, sort]);

  return (
    <UsersContext.Provider
      value={{
        loading,
        data: usersMapped,
        sort,
        filters,
        pagination,
        refetch: loadUsers,
        onSortChange: setSort,
        onFiltersChange: setFilters,
        onPaginationChange: setPagination,
      }}
    >
      {children}
    </UsersContext.Provider>
  );
};
