import { theme } from '@onyma-ds/tokens';
import { FiLogOut } from 'react-icons/fi';
import styled from 'styled-components';

export const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;

  .avatar {
    display: flex;
    align-items: center;
    gap: 0.2rem;
    cursor: pointer;
    padding-right: 1rem;
    font-size: 1.4rem;
  }
`;

export const AvatarContainer = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${theme.colors.gray_80};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
`;

export const DropdownMenu = styled.div`
  position: absolute;
  top: 50px;
  right: 0;
  min-width: 250px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  z-index: 9;
  font-size: 0.9rem;

  hr {
    margin: 0.5rem 0;
    border: 0;
    border-top: 1px solid #ddd;
  }

  span {
    width: 100%;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    &:hover {
      background-color: #f5f5f5;
    }
  }
`;

export const EmailText = styled.div`
  white-space: nowrap;
  padding: 0 0.5rem;
  font-weight: 700;
  color: #333333;
  height: 100%;
`;

export const SignOutButton = styled.button`
  background-color: transparent;
  border: none;
  text-align: left;
  color: ${theme.colors.danger};
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
`;

export const SignOutIcon = styled(FiLogOut).attrs({
  size: 20,
  color: theme.colors.danger,
})``;
