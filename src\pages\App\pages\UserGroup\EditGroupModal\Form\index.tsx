import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Button, Checkbox, InputTextBox, useToast } from '@onyma-ds/react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import {
  EditUserGroupFormType,
  EditUserGroupSchema,
  EditUserGroupType,
} from './validations';

import * as SC from './styles';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useApi } from '@/contexts/api';

export default function EditUserGroupForm({
  defaultValues,
  onClose,
}: EditUserGroupFormType) {
  const {
    userGroup: { editUserGroup },
  } = useApi();
  const queryClient = useQueryClient();
  const toast = useToast();

  const {
    formState: { errors },
    handleSubmit,
    control,
  } = useForm<EditUserGroupType>({
    resolver: zodResolver(EditUserGroupSchema),
    defaultValues: {
      name: defaultValues.nome,
      description: defaultValues.descricao,
      requireCompany: defaultValues.requerEmpresa,
    },
  });

  const { mutate } = useMutation({
    mutationFn: (data: EditUserGroupType) =>
      editUserGroup({
        id: defaultValues.id,
        name: data.name,
        description: data.description,
        requireCompany: !!data.requireCompany,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userGroups'] });
      toast.addToast({
        type: 'success',
        title: 'Grupo editado com sucesso',
        description: 'O grupo foi editado com sucesso',
      });
      onClose();
    },
    onError: () => {
      toast.addToast({
        type: 'error',
        title: 'Erro ao editar grupo',
        description: 'Ocorreu um erro ao editar o grupo',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<EditUserGroupType> = (data) => {
    mutate(data);
  };

  return (
    <SC.Container onSubmit={handleSubmit(handleSubmitForm)}>
      <Controller
        control={control}
        name="name"
        defaultValue={defaultValues.nome}
        render={({ field }) => (
          <InputTextBox
            id="name"
            type="text"
            placeholder="Digite o nome do grupo"
            label="Nome do grupo"
            defaultValue={defaultValues.nome}
            error={!!errors.name}
            feedbackText={errors.name?.message}
            onChangeValue={field.onChange}
          />
        )}
      />
      <Controller
        control={control}
        name="description"
        defaultValue={defaultValues.descricao}
        render={({ field }) => (
          <InputTextBox
            id="description"
            type="text"
            placeholder="Digite a descrição do grupo"
            defaultValue={defaultValues.descricao}
            label="Descrição"
            error={!!errors.description}
            feedbackText={errors.description?.message}
            onChangeValue={field.onChange}
          />
        )}
      />
      <SC.CheckboxWrapper>
        <Controller
          control={control}
          name="requireCompany"
          defaultValue={defaultValues.requerEmpresa}
          render={({ field }) => (
            <Checkbox.Container>
              <Checkbox
                id="requireCompany"
                defaultChecked={defaultValues.requerEmpresa}
                onChange={field.onChange}
              />
              <Checkbox.Label htmlFor="requireCompany">
                Requer empresa
              </Checkbox.Label>
            </Checkbox.Container>
          )}
        />
      </SC.CheckboxWrapper>

      {/* <div>
        <Text as="h2">Permissões</Text>

        <SC.CheckboxWrapper>
          <Controller
            control={control}
            name="permission.start"
            defaultValue={defaultValues.permissions.start}
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="start"
                  defaultChecked={defaultValues.permissions.start}
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="start">Início</Checkbox.Label>
              </Checkbox.Container>
            )}
          />

          <Controller
            control={control}
            name="permission.app"
            defaultValue={defaultValues.permissions.app}
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="appermission.app"
                  defaultChecked={defaultValues.permissions.app}
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="appermission.app">Apps</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.indicators"
            defaultValue={defaultValues.permissions.indicators}
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="inpermission.indicators"
                  defaultChecked={defaultValues.permissions.indicators}
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="inpermission.indicators">
                  Indicadores
                </Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.faq"
            defaultValue={defaultValues.permissions.faq}
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="fpermission.faq"
                  defaultChecked={defaultValues.permissions.faq}
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="fpermission.faq">FAQ</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.system"
            defaultValue={defaultValues.permissions.system}
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="system"
                  defaultChecked={defaultValues.permissions.system}
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="system">Sistema</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
        </SC.CheckboxWrapper>
      </div> */}

      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="secondary"
          type="submit"
          color="white"
        >
          Editar
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
