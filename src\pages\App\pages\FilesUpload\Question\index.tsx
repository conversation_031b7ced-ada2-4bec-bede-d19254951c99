import { Text } from '@onyma-ds/react';
import * as SC from './styles';

type QuestionProps = {
  index: number;
  children: React.ReactNode;
  label: string;
};

export function Question({ index, children, label }: QuestionProps) {
  return (
    <SC.Container>
      <div className="question">
        <span>{index}</span>
        <Text>{label}</Text>
      </div>
      {children}
    </SC.Container>
  );
}
