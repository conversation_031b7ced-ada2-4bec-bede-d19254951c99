import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import SelectCompany from '@/pages/App/components/Header/components/ProfileChange/CompanyCheckCombobox';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, InputBox, InputTextBox, useToast } from '@onyma-ds/react';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { BiDownload, BiUpload } from 'react-icons/bi';
import { LuPaperclip } from 'react-icons/lu';
import UserGroupMultiCombobox from '../../SystemMenus/components/ModalCreate/Form/UserGroupMultiCombobox';
import { LabelAndValueCompromissos } from '../../SystemMenus/components/ModalEdit/Form';
import { LabelAndValue } from '../../Users/<USER>/UserForm/components/CompanyCheckCombobox/types';
import { useLoadOptions } from '../../Users/<USER>/UserForm/hooks/useLoadOptions';
import { Question } from '../Question';
import { RadioContainer } from '../RadioContainer';
import * as SC from './styles';
import { UploadFileSchemaType, uploadFileSchema } from './validations';

export type IFileTypes = {
  id: number;
  modelo: string;
};

export default function FilesUploadPage() {
  const { user } = useAuth();
  const {
    file: { remoteCreateFile },
  } = useApi();

  const { addToast } = useToast();
  const { companiesOptions, profilesOptions } = useLoadOptions();
  const [selectedUsersGroup, setSelectedUsersGroup] = useState<
    LabelAndValueCompromissos[]
  >([]);
  const [selectedValue, setSelectedValue] = useState<IFileTypes | null>(null);

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setError,
    setValue,
    watch,
  } = useForm<UploadFileSchemaType>({
    resolver: zodResolver(uploadFileSchema),
  });

  const isUserAdmin = user?.currentRole.name.includes('Administrador');

  const { mutate } = useMutation({
    mutationFn: (data: UploadFileSchemaType) =>
      remoteCreateFile({
        usuarioCriador: user?.uuid,
        nome: data.fileName,
        arquivo: data.file,
        tipo: String(selectedValue?.id),
        empresa: isUserAdmin
          ? String(data.company?.value)
          : String(user.companyId),
        perfil: isUserAdmin && data.userGroup ? data.userGroup : null,
      }),
    onSuccess: () => {
      setSelectedUsersGroup([]);
      setValue('fileName', '');
      reset();
      addToast({
        title: 'Arquivo enviado com sucesso',
        description: 'O arquivo foi enviado com sucesso',
        type: 'success',
      });
    },
    onError: (error) => {
      addToast({
        title: 'Erro ao enviar arquivo',
        description: error.message,
        type: 'error',
      });
    },
  });

  const handleRadioChange = (fileType: IFileTypes) => {
    setSelectedValue(fileType);
  };

  const onSubmit = (data: UploadFileSchemaType) => {
    if (isUserAdmin) {
      if (!data.company) {
        setError('company', { message: 'Esse campo é obrigatório' });
        return;
      }
      if (!data.userGroup) {
        setError('userGroup', { message: 'Esse campo é obrigatório' });
        return;
      }
    }
    mutate(data);
  };

  return (
    <SC.Container>
      <RadioContainer
        selectedValue={selectedValue}
        handleRadioChange={handleRadioChange}
      />
      <SC.FormWrapper onSubmit={handleSubmit(onSubmit)}>
        <Question
          index={1}
          label="Selecione o tipo do arquivo e faça o download do modelo do arquivo"
        >
          <a
            href={selectedValue?.modelo}
            download
            target="_blank"
            style={{ width: '100%' }}
          >
            <Button
              className="button"
              buttonType="secondary"
              variant="secondary"
              type="button"
              disabled={!selectedValue}
            >
              <BiDownload size={18} />
              Fazer download do modelo do arquivo
            </Button>
          </a>
        </Question>

        <Question
          index={2}
          label="Importe o arquivo após o preenchimento"
        >
          <Controller
            control={control}
            name="file"
            render={({ field }) => (
              <div style={{ width: '100%' }}>
                <SC.FileUploadWrapper>
                  <SC.TextInput
                    key={field.name}
                    type="text"
                    placeholder="Selecione o arquivo"
                    value={field.value?.name ?? ''}
                    disabled
                  />
                  <SC.StyledLabel
                    htmlFor="file"
                    disabled={selectedValue === null}
                  >
                    <span>Selecionar</span>
                    <LuPaperclip />
                  </SC.StyledLabel>
                  <SC.StyledInput
                    disabled={selectedValue === null}
                    key={'file'}
                    id="file"
                    type="file"
                    accept=".xls, .xlsx" // Todo: alterar dinamicamente para o ZOD
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      field.onChange(file);
                    }}
                  />
                </SC.FileUploadWrapper>
                {errors.file && (
                  <SC.ErrorMessage>{errors.file?.message}</SC.ErrorMessage>
                )}
              </div>
            )}
          />
        </Question>

        <Question
          index={3}
          label="Digite o nome do arquivo"
        >
          <Controller
            control={control}
            name="fileName"
            render={({ field }) => (
              <InputTextBox
                key={'fileName'}
                id="fileName"
                type="text"
                placeholder="Insira o nome do arquivo"
                value={field.value}
                error={!!errors.fileName}
                feedbackText={errors.fileName?.message}
                onChangeValue={field.onChange}
                disabled={!watch('file')}
              />
            )}
          />
        </Question>

        {isUserAdmin && (
          <>
            <Question
              index={4}
              label="Selecione a empresa"
            >
              <Controller
                control={control}
                name="company"
                render={({ field }) => (
                  <InputBox>
                    <SelectCompany
                      company={companiesOptions.map((company) => {
                        return {
                          label: company.name,
                          value: company.id,
                          logo: company.logo,
                        };
                      })}
                      selectedCompany={field.value ?? ({} as LabelAndValue)}
                      setSelectedCompany={field.onChange}
                    />
                    {errors.company && (
                      <SC.ErrorMessage>
                        {errors?.company.message}
                      </SC.ErrorMessage>
                    )}
                  </InputBox>
                )}
              />
            </Question>

            <Question
              index={5}
              label="Selecione os perfis"
            >
              <Controller
                control={control}
                name="userGroup"
                render={({ field }) => (
                  <div style={{ width: '100%' }}>
                    <UserGroupMultiCombobox
                      company={
                        profilesOptions.map((company) => {
                          return {
                            label: company.nome,
                            value: company.id,
                            requerEmpresa: company.requerEmpresa,
                          };
                        }) ?? []
                      }
                      onChange={field.onChange}
                      selectedCompanies={selectedUsersGroup}
                      setSelectedCompanies={setSelectedUsersGroup}
                    />
                    {errors.userGroup && (
                      <SC.ErrorMessage>
                        {errors?.userGroup.message}
                      </SC.ErrorMessage>
                    )}
                  </div>
                )}
              />
            </Question>
          </>
        )}

        <Button
          type="submit"
          className="button"
          color="white"
          variant="secondary"
        >
          <BiUpload size={18} />
          Fazer upload do arquivo
        </Button>
      </SC.FormWrapper>
    </SC.Container>
  );
}
