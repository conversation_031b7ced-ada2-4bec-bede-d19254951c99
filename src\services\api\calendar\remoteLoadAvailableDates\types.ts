import { ApiResult } from '@/@types/ApiResult';

type Params = {
  tipoSaida?: string;
  dataInicio?: Date;
  dataFim?: Date;
  codigoAgenda: string;
  statusAgendaFiltro?: string;
  codigoSoc: string;
};

export type AvailableDate = {
  empresa: string;
  nome: string;
  data: string;
  dateTyped: Date;
  horario: string;
  codigoAgenda: string;
  nomeAgenda: string;
  statusAgenda: string;
};

type Result = ApiResult<AvailableDate[]>;

export type RemoteLoadAvailableDates = (params: Params) => Promise<Result>;
