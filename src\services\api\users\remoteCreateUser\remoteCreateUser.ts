import { httpClient } from '@/services/httpClient';
import { RemoteCreateUser } from './types';

export const remoteCreateUser: RemoteCreateUser = async (params) => {
  const response = await httpClient.post('/user/create', {
    nome: params.nome,
    email: params.email,
    senha: params.senha,
    empresas: params.empresas || null,
    perfis: params.perfis,
    ativo: params.ativo,
    matriculaSoc: params.matriculaSoc || null,
    codigoSoc: params.codigoSoc || null,
  });
  return response.data;
};
